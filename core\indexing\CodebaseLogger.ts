import fs from "fs";
import path from "path";
import { getCodebaseIndexingLogsPath, getCodebaseRetrievalLogsPath } from "../util/paths.js";

export type LogLevel = "debug" | "info" | "warn" | "error";

export interface BaseLogEntry {
  timestamp: string;
  level: LogLevel;
  sessionId: string;
  workspaceDir?: string;
}

// 索引流程相关的日志条目类型
export interface IndexingStartLogEntry extends BaseLogEntry {
  type: "indexing_start";
  data: {
    totalFiles: number;
    indexType: string;
    branch?: string;
  };
}

export interface FileProcessingLogEntry extends BaseLogEntry {
  type: "file_processing";
  data: {
    filepath: string;
    fileSize: number;
    language?: string;
    processingTimeMs: number;
    chunksGenerated?: number;
    error?: string;
  };
}

export interface ChunkExtractionLogEntry extends BaseLogEntry {
  type: "chunk_extraction";
  data: {
    filepath: string;
    totalChunks: number;
    chunks: Array<{
      index: number;
      startLine: number;
      endLine: number;
      contentLength: number;
      digest: string;
    }>;
  };
}

export interface VectorizationLogEntry extends BaseLogEntry {
  type: "vectorization";
  data: {
    chunkId: string;
    filepath: string;
    chunkIndex: number;
    embeddingModel: string;
    processingTimeMs: number;
    vectorDimensions?: number;
    error?: string;
  };
}

export interface IndexingCompleteLogEntry extends BaseLogEntry {
  type: "indexing_complete";
  data: {
    totalFiles: number;
    totalChunks: number;
    totalTimeMs: number;
    indexType: string;
    success: boolean;
    errors?: string[];
  };
}

// 检索流程相关的日志条目类型
export interface RetrievalStartLogEntry extends BaseLogEntry {
  type: "retrieval_start";
  data: {
    originalQuery: string;
    fullInput: string;
    nRetrieve: number;
    nFinal: number;
    useReranking: boolean;
    filterDirectory?: string;
  };
}

export interface QueryPreprocessingLogEntry extends BaseLogEntry {
  type: "query_preprocessing";
  data: {
    originalQuery: string;
    processedQuery: string;
    preprocessingSteps: string[];
    processingTimeMs: number;
  };
}

export interface VectorSearchLogEntry extends BaseLogEntry {
  type: "vector_search";
  data: {
    query: string;
    embeddingModel: string;
    searchTimeMs: number;
    resultsCount: number;
    results: Array<{
      filepath: string;
      chunkIndex: number;
      score: number;
      startLine: number;
      endLine: number;
      contentPreview: string; // 前100个字符
    }>;
  };
}

export interface FullTextSearchLogEntry extends BaseLogEntry {
  type: "fulltext_search";
  data: {
    query: string;
    searchTimeMs: number;
    resultsCount: number;
    bm25Threshold: number;
    results: Array<{
      filepath: string;
      chunkIndex: number;
      rank: number;
      startLine: number;
      endLine: number;
      contentPreview: string;
    }>;
  };
}

export interface RerankingLogEntry extends BaseLogEntry {
  type: "reranking";
  data: {
    query: string;
    rerankModel: string;
    inputChunksCount: number;
    outputChunksCount: number;
    processingTimeMs: number;
    scores: number[];
    rerankedResults: Array<{
      filepath: string;
      chunkIndex: number;
      originalScore: number;
      rerankScore: number;
      startLine: number;
      endLine: number;
    }>;
    error?: string;
  };
}

export interface ContextGenerationLogEntry extends BaseLogEntry {
  type: "context_generation";
  data: {
    finalChunksCount: number;
    totalTokens: number;
    contextItems: Array<{
      filepath: string;
      startLine: number;
      endLine: number;
      tokenCount: number;
      contentPreview: string;
    }>;
  };
}

export interface RetrievalCompleteLogEntry extends BaseLogEntry {
  type: "retrieval_complete";
  data: {
    originalQuery: string;
    totalTimeMs: number;
    finalResultsCount: number;
    success: boolean;
    error?: string;
  };
}

export type CodebaseLogEntry = 
  | IndexingStartLogEntry
  | FileProcessingLogEntry
  | ChunkExtractionLogEntry
  | VectorizationLogEntry
  | IndexingCompleteLogEntry
  | RetrievalStartLogEntry
  | QueryPreprocessingLogEntry
  | VectorSearchLogEntry
  | FullTextSearchLogEntry
  | RerankingLogEntry
  | ContextGenerationLogEntry
  | RetrievalCompleteLogEntry;

export interface CodebaseLoggerConfig {
  enabled: boolean;
  logLevel: LogLevel;
  maxLogFileSize: number; // in bytes
  logRetentionDays: number;
  bufferSize: number; // number of entries to buffer before writing
}

const DEFAULT_CONFIG: CodebaseLoggerConfig = {
  enabled: false,
  logLevel: "info",
  maxLogFileSize: 10 * 1024 * 1024, // 10MB
  logRetentionDays: 7,
  bufferSize: 100,
};

export class CodebaseLogger {
  private static instance: CodebaseLogger | null = null;
  private config: CodebaseLoggerConfig;
  private indexingBuffer: CodebaseLogEntry[] = [];
  private retrievalBuffer: CodebaseLogEntry[] = [];
  private sessionId: string;
  private flushTimeout: NodeJS.Timeout | null = null;

  private constructor(config: Partial<CodebaseLoggerConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.sessionId = this.generateSessionId();
  }

  public static getInstance(config?: Partial<CodebaseLoggerConfig>): CodebaseLogger {
    if (CodebaseLogger.instance === null) {
      CodebaseLogger.instance = new CodebaseLogger(config);
    }
    return CodebaseLogger.instance;
  }

  public updateConfig(config: Partial<CodebaseLoggerConfig>): void {
    this.config = { ...this.config, ...config };
  }

  private generateSessionId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private shouldLog(level: LogLevel): boolean {
    if (!this.config.enabled) return false;
    
    const levels: LogLevel[] = ["debug", "info", "warn", "error"];
    const currentLevelIndex = levels.indexOf(this.config.logLevel);
    const entryLevelIndex = levels.indexOf(level);
    
    return entryLevelIndex >= currentLevelIndex;
  }

  private async writeToFile(entries: CodebaseLogEntry[], isIndexing: boolean): Promise<void> {
    if (entries.length === 0) return;

    const logPath = isIndexing ? getCodebaseIndexingLogsPath() : getCodebaseRetrievalLogsPath();
    
    try {
      // Check file size and rotate if necessary
      await this.rotateLogFileIfNeeded(logPath);
      
      const logLines = entries.map(entry => JSON.stringify(entry)).join('\n') + '\n';
      await fs.promises.appendFile(logPath, logLines, 'utf8');
    } catch (error) {
      console.error(`Failed to write codebase log: ${error}`);
    }
  }

  private async rotateLogFileIfNeeded(logPath: string): Promise<void> {
    try {
      const stats = await fs.promises.stat(logPath);
      if (stats.size > this.config.maxLogFileSize) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const rotatedPath = `${logPath}.${timestamp}`;
        await fs.promises.rename(logPath, rotatedPath);
      }
    } catch (error) {
      // File doesn't exist yet, which is fine
    }
  }

  private scheduleFlush(): void {
    if (this.flushTimeout) {
      clearTimeout(this.flushTimeout);
    }
    
    this.flushTimeout = setTimeout(() => {
      this.flush();
    }, 5000); // Flush every 5 seconds
  }

  public async flush(): Promise<void> {
    if (this.indexingBuffer.length > 0) {
      await this.writeToFile([...this.indexingBuffer], true);
      this.indexingBuffer = [];
    }

    if (this.retrievalBuffer.length > 0) {
      await this.writeToFile([...this.retrievalBuffer], false);
      this.retrievalBuffer = [];
    }

    if (this.flushTimeout) {
      clearTimeout(this.flushTimeout);
      this.flushTimeout = null;
    }
  }

  private addToBuffer(entry: CodebaseLogEntry, isIndexing: boolean): void {
    if (!this.shouldLog(entry.level)) return;

    const buffer = isIndexing ? this.indexingBuffer : this.retrievalBuffer;
    buffer.push(entry);

    if (buffer.length >= this.config.bufferSize) {
      // Flush immediately if buffer is full
      this.writeToFile([...buffer], isIndexing);
      buffer.length = 0;
    } else {
      this.scheduleFlush();
    }
  }

  // 索引流程日志记录方法
  public logIndexingStart(data: IndexingStartLogEntry['data'], level: LogLevel = 'info', workspaceDir?: string): void {
    const entry: IndexingStartLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      sessionId: this.sessionId,
      workspaceDir,
      type: 'indexing_start',
      data,
    };
    this.addToBuffer(entry, true);
  }

  public logFileProcessing(data: FileProcessingLogEntry['data'], level: LogLevel = 'debug', workspaceDir?: string): void {
    const entry: FileProcessingLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      sessionId: this.sessionId,
      workspaceDir,
      type: 'file_processing',
      data,
    };
    this.addToBuffer(entry, true);
  }

  public logChunkExtraction(data: ChunkExtractionLogEntry['data'], level: LogLevel = 'debug', workspaceDir?: string): void {
    const entry: ChunkExtractionLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      sessionId: this.sessionId,
      workspaceDir,
      type: 'chunk_extraction',
      data,
    };
    this.addToBuffer(entry, true);
  }

  public logVectorization(data: VectorizationLogEntry['data'], level: LogLevel = 'debug', workspaceDir?: string): void {
    const entry: VectorizationLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      sessionId: this.sessionId,
      workspaceDir,
      type: 'vectorization',
      data,
    };
    this.addToBuffer(entry, true);
  }

  public logIndexingComplete(data: IndexingCompleteLogEntry['data'], level: LogLevel = 'info', workspaceDir?: string): void {
    const entry: IndexingCompleteLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      sessionId: this.sessionId,
      workspaceDir,
      type: 'indexing_complete',
      data,
    };
    this.addToBuffer(entry, true);
  }

  // 检索流程日志记录方法
  public logRetrievalStart(data: RetrievalStartLogEntry['data'], level: LogLevel = 'info', workspaceDir?: string): void {
    const entry: RetrievalStartLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      sessionId: this.sessionId,
      workspaceDir,
      type: 'retrieval_start',
      data,
    };
    this.addToBuffer(entry, false);
  }

  public logQueryPreprocessing(data: QueryPreprocessingLogEntry['data'], level: LogLevel = 'debug', workspaceDir?: string): void {
    const entry: QueryPreprocessingLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      sessionId: this.sessionId,
      workspaceDir,
      type: 'query_preprocessing',
      data,
    };
    this.addToBuffer(entry, false);
  }

  public logVectorSearch(data: VectorSearchLogEntry['data'], level: LogLevel = 'debug', workspaceDir?: string): void {
    const entry: VectorSearchLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      sessionId: this.sessionId,
      workspaceDir,
      type: 'vector_search',
      data,
    };
    this.addToBuffer(entry, false);
  }

  public logFullTextSearch(data: FullTextSearchLogEntry['data'], level: LogLevel = 'debug', workspaceDir?: string): void {
    const entry: FullTextSearchLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      sessionId: this.sessionId,
      workspaceDir,
      type: 'fulltext_search',
      data,
    };
    this.addToBuffer(entry, false);
  }

  public logReranking(data: RerankingLogEntry['data'], level: LogLevel = 'debug', workspaceDir?: string): void {
    const entry: RerankingLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      sessionId: this.sessionId,
      workspaceDir,
      type: 'reranking',
      data,
    };
    this.addToBuffer(entry, false);
  }

  public logContextGeneration(data: ContextGenerationLogEntry['data'], level: LogLevel = 'debug', workspaceDir?: string): void {
    const entry: ContextGenerationLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      sessionId: this.sessionId,
      workspaceDir,
      type: 'context_generation',
      data,
    };
    this.addToBuffer(entry, false);
  }

  public logRetrievalComplete(data: RetrievalCompleteLogEntry['data'], level: LogLevel = 'info', workspaceDir?: string): void {
    const entry: RetrievalCompleteLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      sessionId: this.sessionId,
      workspaceDir,
      type: 'retrieval_complete',
      data,
    };
    this.addToBuffer(entry, false);
  }

  // 清理旧日志文件
  public async cleanupOldLogs(): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.config.logRetentionDays);

    const logDir = path.dirname(getCodebaseIndexingLogsPath());

    try {
      const files = await fs.promises.readdir(logDir);

      for (const file of files) {
        if (file.endsWith('.jsonl')) {
          const filePath = path.join(logDir, file);
          const stats = await fs.promises.stat(filePath);

          if (stats.mtime < cutoffDate) {
            await fs.promises.unlink(filePath);
          }
        }
      }
    } catch (error) {
      console.error(`Failed to cleanup old codebase logs: ${error}`);
    }
  }

  // 获取当前会话ID
  public getSessionId(): string {
    return this.sessionId;
  }

  // 重新生成会话ID（用于新的索引或检索会话）
  public newSession(): string {
    this.sessionId = this.generateSessionId();
    return this.sessionId;
  }
}
