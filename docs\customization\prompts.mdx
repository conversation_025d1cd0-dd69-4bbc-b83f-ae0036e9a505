---
title: "Prompt Blocks"
description: "These are the specialized instructions that shape how models respond:"
---

- **Define interaction patterns** for specific tasks or frameworks
- **Encode domain expertise** for particular technologies
- **Ensure consistent guidance** aligned with organizational practices
- **Can be shared and reused** across multiple assistants
- **Act as automated code reviewers** that ensure consistency across teams

![Prompt Blocks Overview](/images/customization/images/prompts-blocks-overview-17194d870840576f9a0dde548f2c70ec.png)

## Learn More

Prompt blocks have the same syntax as [prompt files](/customization/prompts). The `config.yaml` spec for `prompts` can be found [here](/reference#prompts).
