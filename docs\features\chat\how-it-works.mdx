---
title: "How Chat Works"
description: "Continue's Chat feature provides a conversational interface with AI models directly in your IDE sidebar."
---

## Core Functionality

When you start a chat conversation, Continue:

1. **Gathers Context**: Uses any selected code sections and @-mentioned context
2. **Constructs Prompt**: Combines your input with relevant context
3. **Sends to Model**: Prompts the configured AI model for a response
4. **Streams Response**: Returns the AI response in real-time to the sidebar

## Context Management

### Automatic Context

- Selected code in your editor
- Current file context when relevant
- Previous conversation history in the session

### Manual Context

- `@Codebase` - Search and include relevant code from your project
- `@Docs` - Include documentation context
- `@Files` - Reference specific files
- Custom context providers

## Response Handling

Each code section in the AI response includes action buttons:

- **Apply to current file** - Replace selected code
- **Insert at cursor** - Add code at cursor position
- **Copy** - Copy code to clipboard

## Session Management

- Use `Cmd/Ctrl + L` (VS Code) or `Cmd/Ctrl + J` (JetBrains) to start a new session
- Clears all previous context for a fresh start
- Helpful for switching between different tasks

## Advanced Features

### Prompt Inspection

View the exact prompt sent to the AI model in the [prompt logs](/troubleshooting) for debugging and optimization.

### Context Providers

Learn more about how context providers work:

- [Codebase Context](/customization/overview#codebase-context)
- [Documentation Context](/customization/overview#documentation-context)

---

_Chat is designed to feel like a natural conversation while maintaining full transparency about what context is being used._
