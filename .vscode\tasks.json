{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "tsc:watch",
      "type": "npm",
      "script": "tsc:watch",
      "isBackground": true,
      "problemMatcher": ["$tsc-watch"],
      "group": {
        "kind": "build",
        "isDefault": true
      },
      "presentation": {
        "reveal": "always",
        "panel": "new"
      }
    },
    // Compile and bundle the extension
    {
      "label": "vscode-extension:build",
      "dependsOn": [
        // TSC type checking
        "tsc:watch",
        // To build the React app that is used in the extension
        "vscode-extension:continue-ui:build",
        // // To bundle the code the same way we do for publishing
        "vscode-extension:esbuild",
        // To ensure extension.js is regenerated even if
        // vscode-extension:esbuild was already running in background
        "vscode-extension:esbuild-notify",
        // Start the React app that is used in the extension
        "gui:dev"
      ],
      "group": {
        "kind": "build",
        "isDefault": true
      }
    },
    {
      "label": "vscode-extension:esbuild-notify",
      "dependsOn": ["vscode-extension:esbuild"],
      "type": "npm",
      "script": "esbuild-notify",
      "path": "extensions/vscode"
    },
    {
      "label": "vscode-extension:esbuild",
      "dependsOn": ["vscode-extension:continue-ui:build"],
      "type": "npm",
      "script": "esbuild-watch",
      "path": "extensions/vscode",
      "isBackground": true,
      "problemMatcher": [
        {
          "pattern": [
            {
              "regexp": "> (.*?):([0-9]+):([0-9]+): (warning|error): (.+)$",
              "file": 1,
              "line": 2,
              "column": 3,
              "severity": 4,
              "message": 5
            }
          ],
          "background": {
            "activeOnStart": true,
            "beginsPattern": ">",
            "endsPattern": "VS Code Extension esbuild complete"
          }
        }
      ]
    },
    {
      "label": "vscode-extension:package",
      "dependsOn": ["vscode-extension:esbuild"],
      "type": "npm",
      "script": "package",
      "path": "extensions/vscode",
      "problemMatcher": [
        {
          "pattern": [
            {
              "regexp": "> (.*?):([0-9]+):([0-9]+): (warning|error): (.+)$",
              "file": 1,
              "line": 2,
              "column": 3,
              "severity": 4,
              "message": 5
            }
          ]
        }
      ]
    },
    {
      "label": "vscode-extension:continue-ui:build",
      "type": "shell",
      "command": "node",
      "args": ["${workspaceFolder}/extensions/vscode/scripts/prepackage.js"],
      "problemMatcher": ["$tsc"],
      "presentation": {
        "revealProblems": "onProblem",
        "clear": true
      },
      "options": {
        "env": {
          "SKIP_INSTALLS": "true"
        },
        "cwd": "${workspaceFolder}/extensions/vscode"
      }
    },
    // Install or update all dependencies for all projects in the monrepo
    {
      "label": "install-all-dependencies",
      "type": "shell",
      "windows": { "command": "./scripts/install-dependencies.ps1" },
      "command": "./scripts/install-dependencies.sh",
      "problemMatcher": [] // Empty so users are not prompted to select progress reporting
    },
    //
    // Start the React App for debugging with Vite
    {
      "label": "gui:dev",
      "type": "shell",
      "command": "npm",
      "options": {
        "cwd": "${workspaceFolder}/gui"
      },
      "args": ["run", "dev"],
      "isBackground": true,
      "problemMatcher": [
        {
          "pattern": [
            {
              "regexp": ".",
              "file": 1,
              "location": 2,
              "message": 3
            }
          ],
          "background": {
            "activeOnStart": true,
            "beginsPattern": ".",
            "endsPattern": "."
          }
        }
      ]
    },
    //
    // esbuild for the core binary
    {
      "label": "binary:esbuild",
      "type": "shell",
      "command": "npm",
      "args": ["run", "esbuild"],
      "problemMatcher": [],
      "options": {
        "cwd": "binary"
      }
    },
    {
      "label": "docs:start",
      "type": "shell",
      "command": "npm",
      "args": ["run", "start", "--", "--no-open"],
      "problemMatcher": [],
      "options": {
        "cwd": "docs"
      }
    },
    {
      "label": "clean",
      "type": "shell",
      "command": "node",
      "args": ["${workspaceFolder}/scripts/uninstall.js"],
      "problemMatcher": []
    },
    {
      "label": "refresh-dependencies:core",
      "type": "shell",
      "command": "npm",
      "args": ["install"],
      "problemMatcher": [],
      "options": {
        "cwd": "${workspaceFolder}/core"
      },
      "presentation": {
        "close": true
      }
    },
    {
      "label": "refresh-dependencies:vscode",
      "type": "shell",
      "command": "npm",
      "args": ["install"],
      "problemMatcher": [],
      "options": {
        "cwd": "${workspaceFolder}/extensions/vscode"
      },
      "presentation": {
        "close": true
      }
    },
    {
      "label": "refresh-dependencies:gui",
      "type": "shell",
      "command": "npm",
      "args": ["install"],
      "problemMatcher": [],
      "options": {
        "cwd": "${workspaceFolder}/gui"
      },
      "presentation": {
        "close": true
      }
    },
    {
      "label": "refresh-dependencies:all",
      "dependsOn": [
        "refresh-dependencies:core",
        "refresh-dependencies:gui",
        "refresh-dependencies:vscode"
      ],
      "type": "shell",
      "problemMatcher": []
    }
  ]
}
