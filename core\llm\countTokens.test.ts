// @ts-nocheck
// Generated by continue
import { ChatMessage, MessagePart, ToolCall } from "../index.js";
import {
  compileChatMessages,
  countTokens,
  countTokensAsync,
  extractToolSequence,
  pruneLinesFromBottom,
  pruneLinesFromTop,
  pruneRawPromptFromTop,
  pruneStringFromBottom,
  pruneStringFromTop,
} from "./countTokens.js";

describe.skip("countTokens", () => {
  it("should count tokens for a simple string", () => {
    const content = "Hello world!";
    const tokenCount = countTokens(content, "gpt-4");
    expect(tokenCount).toBeGreaterThan(0);
  });

  it("should count tokens for an array of MessagePart", () => {
    const content: MessagePart[] = [{ type: "text", text: "Hello world!" }];
    const tokenCount = countTokens(content, "gpt-4");
    expect(tokenCount).toBeGreaterThan(0);
  });
});

describe.skip("countTokensAsync", () => {
  it("should count tokens asynchronously for a simple string", async () => {
    const content = "Hello world!";
    const tokenCount = await countTokensAsync(content, "gpt-4");
    expect(tokenCount).toBeGreaterThan(0);
  });

  it("should count tokens asynchronously for an array of MessagePart", async () => {
    const content: MessagePart[] = [{ type: "text", text: "Hello world!" }];
    const tokenCount = await countTokensAsync(content, "gpt-4");
    expect(tokenCount).toBeGreaterThan(0);
  });
});

describe.skip("pruneLinesFromTop", () => {
  it("should prune lines from the top to fit within max tokens", () => {
    const prompt = "Line 1\nLine 2\nLine 3\nLine 4";
    const pruned = pruneLinesFromTop(prompt, 5, "gpt-4");
    expect(pruned.split("\n").length).toBeLessThan(prompt.split("\n").length);
  });

  it("should return the original prompt if it's within max tokens", () => {
    const prompt = "Line 1\nLine 2";
    const pruned = pruneLinesFromTop(prompt, 10, "gpt-4");
    expect(pruned).toEqual(prompt);
  });

  it("should return an empty string if maxTokens is 0", () => {
    const prompt = "Line 1\nLine 2\nLine 3\nLine 4";
    const pruned = pruneLinesFromTop(prompt, 0, "gpt-4");
    expect(pruned).toEqual("");
  });

  it("should handle an empty prompt string", () => {
    const prompt = "";
    const pruned = pruneLinesFromTop(prompt, 5, "gpt-4");
    expect(pruned).toEqual("");
  });

  it("should handle a prompt with a single line that exceeds maxTokens", () => {
    const prompt =
      "This is a single long line that will exceed the token limit";
    const pruned = pruneLinesFromTop(prompt, 5, "gpt-4");

    expect(pruned).toEqual("");
  });

  it("should correctly prune when all lines together exceed maxTokens but individual lines do not", () => {
    const prompt = "L1\nL2\nL3\nL4";

    const pruned = pruneLinesFromTop(prompt, 5, "gpt-4");
    expect(pruned).toEqual("L3\nL4");
  });
});

describe.skip("pruneLinesFromBottom", () => {
  it("should prune lines from the bottom to fit within max tokens", () => {
    const prompt = "Line 1\nLine 2\nLine 3\nLine 4";
    const pruned = pruneLinesFromBottom(prompt, 5, "gpt-4");
    expect(pruned.split("\n").length).toBeLessThan(prompt.split("\n").length);
  });

  it("should return the original prompt if it's within max tokens", () => {
    const prompt = "Line 1\nLine 2";
    const pruned = pruneLinesFromBottom(prompt, 10, "gpt-4");
    expect(pruned).toEqual(prompt);
  });

  it("should return an empty string if maxTokens is 0", () => {
    const prompt = "Line 1\nLine 2\nLine 3\nLine 4";
    const pruned = pruneLinesFromBottom(prompt, 0, "gpt-4");
    expect(pruned).toEqual("");
  });

  it("should handle an empty prompt string", () => {
    const prompt = "";
    const pruned = pruneLinesFromBottom(prompt, 5, "gpt-4");
    expect(pruned).toEqual("");
  });

  it("should handle a prompt with a single line that exceeds maxTokens", () => {
    const prompt =
      "This is a single long line that will exceed the token limit";
    const pruned = pruneLinesFromBottom(prompt, 5, "gpt-4");

    expect(pruned).toEqual("");
  });

  it("should correctly prune when all lines together exceed maxTokens but individual lines do not", () => {
    const prompt = "L1\nL2\nL3\nL4";

    const pruned = pruneLinesFromBottom(prompt, 5, "gpt-4");
    expect(pruned).toEqual("L1\nL2");
  });
});

describe.skip("pruneRawPromptFromTop", () => {
  it("should prune string from the top based on maxTokens", () => {
    const result = pruneRawPromptFromTop("gpt-4", 5, "Hello world!", 2);
    expect(result.length).toBeLessThan("Hello world!".length);
  });
});

describe.skip("pruneStringFromTop", () => {
  it("should prune string from the top based on maxTokens", () => {
    const result = pruneStringFromTop("gpt-4", 5, "Hello world!");
    expect(result.length).toBeLessThan("Hello world!".length);
  });
});

describe.skip("pruneStringFromBottom", () => {
  it("should prune string from the bottom based on maxTokens", () => {
    const result = pruneStringFromBottom("gpt-4", 5, "Hello world!");
    expect(result.length).toBeLessThan("Hello world!".length);
  });
});

describe.skip("compileChatMessages", () => {
  it("should compile and handle an empty or undefined message list", () => {
    const compiled = compileChatMessages("gpt-4", undefined, 100, 50, false);
    expect(compiled.length).toBe(0);
  });

  it("should compile chat messages without truncating if within context length", () => {
    const msgs: ChatMessage[] = [
      { role: "user", content: "Hello world!" },
      { role: "assistant", content: "Hi there!" },
    ];
    const compiled = compileChatMessages("gpt-4", msgs, 100, 10, false);
    expect(compiled.length).toBe(2);
  });

  it("should throw an error if maxTokens is close to or exceeds contextLength", () => {
    expect(() => {
      compileChatMessages("gpt-4", [], 100, 90, false);
    }).toThrow();
  });

  it("should filter out any empty or system messages", () => {
    const msgs: ChatMessage[] = [
      { role: "system", content: "" },
      { role: "user", content: "" },
      { role: "assistant", content: "Hi there!" },
    ];
    const compiled = compileChatMessages("gpt-4", msgs, 100, 10, false);
    expect(compiled.length).toBe(1);
  });
});

describe("extractToolSequence", () => {
  // Helper function to create mock messages
  const createUserMessage = (content: string): ChatMessage => ({
    role: "user",
    content,
  });

  const createAssistantMessage = (
    content: string,
    toolCalls?: ToolCall[],
  ): ChatMessage => ({
    role: "assistant",
    content,
    toolCalls,
  });

  const createToolMessage = (
    content: string,
    toolCallId: string,
  ): ChatMessage => ({
    role: "tool",
    content,
    toolCallId,
  });

  const createToolCall = (
    id: string,
    name: string,
    args: object,
  ): ToolCall => ({
    id,
    type: "function",
    function: {
      name,
      arguments: JSON.stringify(args),
    },
  });

  test("extractToolSequence should handle a single user message", () => {
    const messages: ChatMessage[] = [createUserMessage("Hello world!")];
    const sequence = extractToolSequence(messages);

    expect(sequence).toHaveLength(1);
    expect(sequence[0].role).toBe("user");
    expect(sequence[0].content).toBe("Hello world!");
    expect(messages).toHaveLength(0); // Original array should be modified
  });

  test("extractToolSequence should handle a single tool message with assistant", () => {
    const toolCall = createToolCall("tc_1", "read_file", { path: "test.txt" });
    const messages: ChatMessage[] = [
      createAssistantMessage("I'll read the file for you.", [toolCall]),
      createToolMessage("File contents here", "tc_1"),
    ];
    const sequence = extractToolSequence(messages);

    expect(sequence).toHaveLength(2);
    expect(sequence[0].role).toBe("assistant");
    expect(sequence[1].role).toBe("tool");
    expect(sequence[1].toolCallId).toBe("tc_1");
    expect(messages).toHaveLength(0);
  });

  test("extractToolSequence should handle multiple consecutive tool messages", () => {
    const toolCall1 = createToolCall("tc_1", "read_file", { path: "test.txt" });
    const toolCall2 = createToolCall("tc_2", "write_file", {
      path: "output.txt",
      content: "data",
    });
    const messages: ChatMessage[] = [
      createAssistantMessage("I'll read and write files.", [
        toolCall1,
        toolCall2,
      ]),
      createToolMessage("File contents 1", "tc_1"),
      createToolMessage("File written successfully", "tc_2"),
    ];
    const sequence = extractToolSequence(messages);

    expect(sequence).toHaveLength(3);
    expect(sequence[0].role).toBe("assistant");
    expect(sequence[1].role).toBe("tool");
    expect(sequence[1].toolCallId).toBe("tc_1");
    expect(sequence[2].role).toBe("tool");
    expect(sequence[2].toolCallId).toBe("tc_2");
    expect(messages).toHaveLength(0);
  });

  test("extractToolSequence should handle tool sequence with previous context", () => {
    const toolCall = createToolCall("tc_1", "search", { query: "test" });
    const messages: ChatMessage[] = [
      createUserMessage("What's in the codebase?"),
      createAssistantMessage("Let me search for you.", [toolCall]),
      createToolMessage("Search results here", "tc_1"),
    ];
    const sequence = extractToolSequence(messages);

    expect(sequence).toHaveLength(2);
    expect(sequence[0].role).toBe("assistant");
    expect(sequence[1].role).toBe("tool");
    expect(messages).toHaveLength(1); // User message should remain
    expect(messages[0].role).toBe("user");
  });

  test("extractToolSequence should throw error when tool message has no matching tool call", () => {
    const toolCall = createToolCall("tc_1", "read_file", { path: "test.txt" });
    const messages: ChatMessage[] = [
      createAssistantMessage("I'll read the file for you.", [toolCall]),
      createToolMessage("File contents here", "tc_2"), // Wrong tool call ID
    ];

    expect(() => extractToolSequence(messages)).toThrow(
      'Error parsing chat history: no tool call found to match tool output for id "tc_2"',
    );
  });

  test("extractToolSequence should throw error when last message is not user or tool", () => {
    const messages: ChatMessage[] = [
      createUserMessage("Hello"),
      createAssistantMessage("Hi there!"),
    ];

    expect(() => extractToolSequence(messages)).toThrow(
      "Error parsing chat history: no user/tool message found",
    );
  });

  test("extractToolSequence should throw error when messages array is empty", () => {
    const messages: ChatMessage[] = [];

    expect(() => extractToolSequence(messages)).toThrow(
      "Error parsing chat history: no user/tool message found",
    );
  });

  test("extractToolSequence should handle tool sequence without assistant message", () => {
    const messages: ChatMessage[] = [
      createToolMessage("Orphaned tool message", "tc_1"),
    ];
    const sequence = extractToolSequence(messages);

    expect(sequence).toHaveLength(1);
    expect(sequence[0].role).toBe("tool");
    expect(sequence[0].toolCallId).toBe("tc_1");
    expect(messages).toHaveLength(0);
  });

  test("extractToolSequence should handle complex tool sequence with multiple tool calls", () => {
    const toolCall1 = createToolCall("tc_1", "read_file", {
      path: "file1.txt",
    });
    const toolCall2 = createToolCall("tc_2", "read_file", {
      path: "file2.txt",
    });
    const toolCall3 = createToolCall("tc_3", "write_file", {
      path: "output.txt",
      content: "merged",
    });

    const messages: ChatMessage[] = [
      createUserMessage("Merge these files"),
      createAssistantMessage("I'll read both files and merge them.", [
        toolCall1,
        toolCall2,
        toolCall3,
      ]),
      createToolMessage("Contents of file1", "tc_1"),
      createToolMessage("Contents of file2", "tc_2"),
      createToolMessage("Files merged successfully", "tc_3"),
    ];

    const sequence = extractToolSequence(messages);

    expect(sequence).toHaveLength(4);
    expect(sequence[0].role).toBe("assistant");
    expect(sequence[1].role).toBe("tool");
    expect(sequence[1].toolCallId).toBe("tc_1");
    expect(sequence[2].role).toBe("tool");
    expect(sequence[2].toolCallId).toBe("tc_2");
    expect(sequence[3].role).toBe("tool");
    expect(sequence[3].toolCallId).toBe("tc_3");
    expect(messages).toHaveLength(1); // User message should remain
  });
});
