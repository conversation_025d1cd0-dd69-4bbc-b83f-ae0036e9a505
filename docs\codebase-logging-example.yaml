# Continue配置示例 - 启用Codebase日志记录
# 将此配置添加到您的 ~/.continue/config.yaml 文件中

models:
  - title: "GPT-4"
    provider: "openai"
    model: "gpt-4"
    apiKey: "your-api-key"
  
  - title: "Text Embedding Ada 002"
    provider: "openai"
    model: "text-embedding-ada-002"
    apiKey: "your-api-key"

# Codebase日志记录配置
codebaseLogging:
  # 启用codebase日志记录
  enabled: true
  
  # 日志级别: "debug" | "info" | "warn" | "error"
  # debug: 记录所有详细信息（文件处理、代码块提取、向量化等）
  # info: 记录主要流程（索引开始/完成、检索开始/完成）
  # warn: 仅记录警告和错误
  # error: 仅记录错误
  logLevel: "debug"
  
  # 单个日志文件的最大大小（字节）
  # 当文件超过此大小时，会自动轮转

  # 达到此数量时会立即写入文件，否则每5秒写入一次
  bufferSize: 100

# 其他配置...
contextProviders:
  - name: "codebase"
    params:
      nRetrieve: 25
      nFinal: 10
      useReranking: true

# 模型角色配置
selectedModelByRole:
  default: "GPT-4"
  embed: "Text Embedding Ada 002"
  # 如果有重排序模型，也可以配置
  # rerank: "your-rerank-model"
