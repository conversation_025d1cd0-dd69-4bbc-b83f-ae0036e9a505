{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext", "ES2021"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "noEmitOnError": false, "types": ["jest", "node"], "outDir": "dist", "declaration": true, "declarationMap": true}, "include": ["./**/*.ts", "**/*.js", "./**/*.d.ts"], "exclude": ["test", "dist"]}