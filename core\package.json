{"name": "@continuedev/core", "version": "1.1.0", "description": "The Continue Core contains functionality that can be shared across web, VS Code, or Node.js", "scripts": {"test": "cross-env NODE_OPTIONS=--experimental-vm-modules jest", "vitest": "vitest run", "test:coverage": "cross-env NODE_OPTIONS=--experimental-vm-modules jest --coverage && open ./coverage/lcov-report/index.html", "tsc:check": "tsc -p ./ --noEmit", "build:npm": "tsc -p ./tsconfig.npm.json", "lint": "eslint . --ext ts", "lint:fix": "eslint . --ext ts --fix"}, "type": "module", "author": "Continue Dev, Inc", "license": "Apache-2.0", "devDependencies": {"@babel/preset-env": "^7.24.7", "@biomejs/biome": "1.6.4", "@google/generative-ai": "^0.11.4", "@shikijs/colorized-brackets": "^3.7.0", "@shikijs/transformers": "^3.7.0", "@types/diff": "^7.0.1", "@types/follow-redirects": "^1.14.4", "@types/jest": "^29.5.12", "@types/jquery": "^3.5.29", "@types/jsdom": "^21.1.6", "@types/json-schema": "^7.0.15", "@types/mozilla-readability": "^0.2.1", "@types/mustache": "^4.2.5", "@types/node-fetch": "^2.6.11", "@types/pg": "^8.11.6", "@types/plist": "^3.0.5", "@types/request": "^2.48.12", "@types/tar": "^6.1.13", "@types/uuid": "^9.0.7", "@types/win-ca": "^3.5.4", "cross-env": "^7.0.3", "esbuild": "0.17.19", "eslint": "^8", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "myers-diff": "^2.1.0", "onnxruntime-common": "1.14.0", "onnxruntime-web": "1.14.0", "shiki": "^3.6.0", "ts-jest": "^29.1.1", "typescript": "^5.6.3", "vitest": "^3.1.4"}, "dependencies": {"@aws-sdk/client-bedrock-runtime": "^3.779.0", "@aws-sdk/client-sagemaker-runtime": "^3.777.0", "@aws-sdk/credential-providers": "^3.778.0", "@continuedev/config-types": "^1.0.13", "@continuedev/config-yaml": "file:../packages/config-yaml", "@continuedev/fetch": "^1.0.14", "@continuedev/llm-info": "^1.0.8", "@continuedev/openai-adapters": "file:../packages/openai-adapters", "@modelcontextprotocol/sdk": "^1.12.0", "@mozilla/readability": "^0.5.0", "@octokit/rest": "^20.1.1", "@typescript-eslint/eslint-plugin": "^7.8.0", "@xenova/transformers": "2.14.0", "adf-to-md": "^1.1.0", "async-mutex": "^0.5.0", "axios": "^1.6.7", "cheerio": "^1.0.0-rc.12", "commander": "^12.0.0", "comment-json": "^4.2.3", "dbinfoz": "^0.14.0", "diff": "^7.0.0", "dotenv": "^16.4.5", "fastest-levenshtein": "^1.0.16", "filtrex": "^3.1.0", "follow-redirects": "^1.15.5", "google-auth-library": "^9.14.2", "handlebars": "^4.7.8", "http-proxy-agent": "^7.0.1", "https-proxy-agent": "^7.0.3", "ignore": "^5.3.1", "is-localhost-ip": "^2.0.0", "jinja-js": "0.1.8", "js-tiktoken": "^1.0.8", "jsdom": "^24.0.0", "launchdarkly-node-client-sdk": "^3.2.0", "llm-code-highlighter": "^0.0.14", "lru-cache": "^11.0.2", "mac-ca": "^3.1.0", "node-fetch": "^3.3.2", "node-html-markdown": "^1.3.0", "ollama": "^0.4.6", "onnxruntime-node": "1.14.0", "openai": "^4.104.0", "p-limit": "^6.1.0", "partial-json": "^0.1.7", "pg": "^8.11.3", "plist": "^3.1.0", "posthog-node": "^3.6.3", "puppeteer": "^22.4.0", "puppeteer-chromium-resolver": "^23.0.0", "quick-lru": "^7.0.0", "replicate": "^0.26.0", "request": "^2.88.2", "socket.io-client": "^4.7.3", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "system-ca": "^1.0.3", "tar": "^7.4.3", "tree-sitter-wasms": "^0.1.11", "uuid": "^9.0.1", "vectordb": "^0.4.20", "web-tree-sitter": "^0.21.0", "win-ca": "^3.5.1", "wink-nlp-utils": "^2.1.0", "winston": "^3.17.0", "workerpool": "^9.1.3", "yaml": "^2.4.2", "zod": "^3.24.2"}, "engine-strict": true, "engines": {"node": ">=20.19.0"}}