// Generated by continue

import { incrementalParseJson } from "./incrementalParseJson";

describe("incrementalParseJson", () => {
  it("should return [true, parsedJson] for valid JSON object", () => {
    const raw = '{"key": "value"}';
    const [isValid, parsed] = incrementalParseJson(raw);
    expect(isValid).toBe(true);
    expect(parsed).toEqual({ key: "value" });
  });

  it("should return [true, parsedJson] for valid JSON array", () => {
    const raw = "[1, 2, 3]";
    const [isValid, parsed] = incrementalParseJson(raw);
    expect(isValid).toBe(true);
    expect(parsed).toEqual([1, 2, 3]);
  });

  it("should return [false, partialParsedJson] for incomplete JSON object", () => {
    const raw = '{"key": "value"';
    const [isValid, parsed] = incrementalParseJson(raw);
    expect(isValid).toBe(false);
    expect(parsed).toEqual({ key: "value" });
  });

  it("should return [false, partialParsedJson] for incomplete JSON array", () => {
    const raw = "[1, 2, 3";
    const [isValid, parsed] = incrementalParseJson(raw);
    expect(isValid).toBe(false);
    expect(parsed).toEqual([1, 2, 3]);
  });

  it("should return [false, {}] for completely invalid JSON", () => {
    const raw = "Hello, World!";
    const [isValid, parsed] = incrementalParseJson(raw);
    expect(isValid).toBe(false);
    expect(parsed).toEqual({});
  });

  it("should return [false, {}] for empty string", () => {
    const raw = "";
    const [isValid, parsed] = incrementalParseJson(raw);
    expect(isValid).toBe(false);
    expect(parsed).toEqual({});
  });

  it("should return [false, {}] for invalid JSON syntax", () => {
    const raw = "{key: 'value'}";
    const [isValid, parsed] = incrementalParseJson(raw);
    expect(isValid).toBe(false);
    expect(parsed).toEqual({});
  });

  it("should handle whitespace-only string", () => {
    const raw = "   ";
    const [isValid, parsed] = incrementalParseJson(raw);
    expect(isValid).toBe(false);
    expect(parsed).toEqual({});
  });

  it("should handle numeric string", () => {
    const raw = "12345";
    const [isValid, parsed] = incrementalParseJson(raw);
    expect(isValid).toBe(true);
    expect(parsed).toEqual(12345);
  });

  it("should handle boolean true string", () => {
    const raw = "true";
    const [isValid, parsed] = incrementalParseJson(raw);
    expect(isValid).toBe(true);
    expect(parsed).toEqual(true);
  });

  it("should handle boolean false string", () => {
    const raw = "false";
    const [isValid, parsed] = incrementalParseJson(raw);
    expect(isValid).toBe(true);
    expect(parsed).toEqual(false);
  });

  it("should handle null string", () => {
    const raw = "null";
    const [isValid, parsed] = incrementalParseJson(raw);
    expect(isValid).toBe(true);
    expect(parsed).toBeNull();
  });
});
