{"version": 3, "file": "processors.d.ts", "sourceRoot": "", "sources": ["../src/processors.js"], "names": [], "mappings": ";;;;AAsKA;;;;GAIG;AACH;IACI;;;;OAIG;IACH,yBAGC;IADG,YAAoB;CAE3B;AAED;;;;;GAKG;AAEH;;;;GAIG;AACH;IAEI;;;;;;;;;;;;OAYG;IACH;QAT4B,UAAU,EAA3B,MAAM,EAAE;QACS,SAAS,EAA1B,MAAM,EAAE;QACQ,UAAU,EAA1B,OAAO;QACQ,cAAc,EAA7B,MAAM;QACU,YAAY,EAA5B,OAAO;QACS,SAAS,EAAzB,OAAO;QACQ,QAAQ,EAAvB,MAAM;QACS,IAAI,EAAnB,MAAM;OA+BhB;IA1BG,gBAA4D;IAC5D,eAAyD;IAEzD,cAAyC;IACzC,gBAAgD;IAChD,oBAA6D;IAC7D,kBAA4C;IAE5C,eAAsC;IACtC,kBAA4C;IAC5C,UAA4B;IAC5B,uBAAkF;IAElF,oBAAgD;IAChD,eAAsC;IACtC,oBAAwD;IACxD,oBAAgD;IAEhD,cAAoC;IACpC,YAAgC;IASpC;;;;;;;OAOG;IACH,iBALW,QAAQ;gBACA,MAAM;eAAQ,MAAM;kBAC5B,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAC5B,QAAQ,QAAQ,CAAC,CAsB7B;IAGD;;;;;OAKG;IACH,mBAJW,QAAQ,mBACR,MAAM,GACJ,QAAQ,QAAQ,CAAC,CAgC7B;IAED;;;;;;;;;;OAUG;IACH,qBATW,YAAY,WACZ,MAAM,EAAE,WACR;QAAC,KAAK,EAAC,MAAM,CAAC;QAAC,MAAM,EAAC,MAAM,CAAA;KAAC,GAAC,MAAM;QAEH,IAAI,GAArC,UAAU,GAAC,WAAW;QACJ,MAAM,GAAxB,OAAO;QACU,eAAe,GAAhC,MAAM;QACJ,CAAC,YAAY,EAAE,MAAM,EAAE,CAAC,CA6EpC;IAED;;;;OAIG;IACH,mBAHW,YAAY,GACV,IAAI,CAMhB;IAED;;;;;;OAMG;IACH,oCAJW,QAAQ,QACR,GAAG,GACD,CAAC,MAAM,EAAE,MAAM,CAAC,CA+D5B;IAED;;;;OAIG;IACH,cAHW,QAAQ,GACN,QAAQ,QAAQ,CAAC,CAO7B;IAED;;;;;OAKG;IAEH;;;;;;OAMG;IACH,kBAJW,QAAQ;;;;;;;;;;;;sBANL,MAAM;OA4GnB;IAED;;;;;;;OAOG;IACH,cAJW,QAAQ,EAAE,WACP,GAAG,KACJ,QAAQ,2BAA2B,CAAC,CAqBhD;CAEJ;AAED;IAEI;;;;;;OAMG;IACH,gEAJW,MAAM,EAAE,EAAE,GAER;QAAC,YAAY,EAAE,MAAM,CAAC;QAAC,MAAM,EAAE,MAAM,EAAE,CAAA;KAAC,EAAE,CAwDtD;CACJ;AACD;CAAgE;AAChE;CAAkE;AAClE;CAAmE;AACnE;CAAmE;AACnE;CAA0E;AAC1E;CAAmE;AACnE;IACI,yBAOC;IAJG;;OAEG;IACH,cAAmD;IAGvD,iCA4BC;CACJ;AACD;CAAwE;AACxE;CAAkE;AAClE;CAAgE;AAEhE;CAAwE;AACxE;IA/qBA;;;;;;;;;;OAUG;IACH;;;qFAmEC;CAsmBA;AACD;CAAmE;AACnE;CAAmE;AACnE;IACI,8FAuBC;CACJ;AACD;CAAmE;AAEnE;;;;GAIG;AAEH;;;;GAIG;AACH;IACI;;;;;OAKG;IACH,cAHW,QAAQ,EAAE,GACR,QAAQ,0BAA0B,CAAC,CAgB/C;IAnvBL;;;;;;;;;;OAUG;IACH;;;qFAmEC;IAorBG;;;;;;;OAOG;IACH,wCANW,MAAM,eACN,MAAM,yBACN,MAAM,cACN,MAAM,GACJ,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,CA6B1C;IAED;;;;;;;;OAQG;IACH,oCAPW,UAAU,cACV,MAAM,EAAE,KACR,MAAM,mBACN,MAAM,gCACN,MAAM,GACJ,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,CAmC/B;IAED;;;;;;;;;;OAUG;IACH,6BATW,MAAM,EAAE,eACR,MAAM,EAAE,eACR,MAAM,EAAE,kBACR,MAAM,+BACN,MAAM,sBACN,IAAI,MAAM,CAAC,gBACX,MAAM,EAAE,GACN,CAAC,MAAM,EAAE,MAAM;QAAC,EAAE,EAAE,MAAM,CAAC;QAAC,QAAQ,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,MAAM,CAAA;KAAC,CAAC,CAAC,CAkG1E;IAED;;;;;;;;;OASG;IACH,6DAPW,MAAM,mBACN,MAAM,gCACN,MAAM,sBACN,IAAI,MAAM,CAAC,iBACX,MAAM,EAAE,EAAE,GACR,MAAM;QAAE,YAAY,EAAE,MAAM,CAAC;QAAC,aAAa,EAAE,MAAM;YAAC,EAAE,EAAE,MAAM,CAAC;YAAC,QAAQ,EAAE,MAAM,CAAC;YAAC,KAAK,EAAE,MAAM,CAAA;SAAC,CAAC,CAAA;KAAC,CAAC,CAuE/G;IAED,2CAGC;CACJ;AAED;IAvhCA;;;;;;;;;;OAUG;IACH;;;qFAmEC;CA88BA;AAED;;;;;;;GAOG;AAEH;IAEI;;;;;;OAMG;IACH,mCALW,GAAG,kBACH,aAAa,wBACb,aAAa,GACX,MAAM,CA0ClB;IAED;;;;;OAKG;IACH,+BAJW,GAAG,gBACH,MAAM,GACJ,MAAM,CAoBlB;IACD;;;;;;;;;OASG;IACH,cATW,GAAG,EAAE,iBACL,GAAG,iBAGH,GAAG,GAGD,QAAQ,uBAAuB,CAAC,CAqB5C;IAED;;;;;;;;;;;;OAYG;IACH,0BAXW,MAAM,kBACN,MAAM,EAAE,EAAE,wBACV,MAAM,EAAE,EAAE;QAEO,cAAc,GAA/B,MAAM;QACY,QAAQ,GAA1B,OAAO;QACU,QAAQ;YACC,MAAM,GAAhC,MAAM;YACoB,KAAK,GAA/B,MAAM;;QACJ,MAAM,EAAE,CAwDpB;CACJ;AAED;IACI,8FAiBC;CACJ;AAED;IACI;;;;;;OAMG;IACH,cAJW,QAAQ,EAAE,WACV,QAAQ,EAAE,GACR,QAAQ,2BAA2B,CAAC,CAiChD;CACJ;AAED;IAEI,yBAeC;IADG,qBAAwD;IAG5D;;;;OAIG;IACH,kCAHW,YAAY,GAAC,YAAY;cAChB,YAAY;cAAQ,MAAM,EAAE;MAyB/C;IAED;;;;OAIG;IACH,aAHW,YAAY,GAAC,YAAY,GACvB,QAAQ;QAAE,cAAc,EAAE,MAAM,CAAA;KAAE,CAAC,CA2B/C;CACJ;AAED;IAEI;;;OAGG;IACH,uCAHW,YAAY,GACV,YAAY,CAQxB;IAED;;;;OAIG;IACH,aAHW,YAAY,GAAC,YAAY;sBACC,MAAM;wBAAkB,MAAM;OAsBlE;CACJ;AAED;IAGI,yBA2BC;IARG,wBAA8B;IAE9B,qBAEE;IAEF,UAA4B;IAC5B,SAA0B;IAG9B;;;;;OAKG;IACH,kCAJW,YAAY,GAAC,YAAY,cACzB,MAAM,GACJ;QAAC,IAAI,EAAE,YAAY,CAAC;QAAC,IAAI,EAAE,MAAM,EAAE,CAAA;KAAC,CAwBhD;IAGD;;;;OAIG;IACH,aAHW,YAAY,GAAC,YAAY,GACvB,QAAQ;QAAE,YAAY,EAAE,MAAM,CAAA;KAAE,CAAC,CAoB7C;CACJ;AAED;IAEI,yBAyBC;IAtBG,wBAQC;IAED,+BAQC;IAED,qBAAkE;IAKtE;;;;;;;;;;;;;;;;;;;OAmBG;IACH,yBANW,YAAY,GAAC,YAAY,cACzB,MAAM,cACN,MAAM,WACN,MAAM;cACI,YAAY;cAAQ,MAAM,EAAE;gBAAU,OAAO;MAiDjE;IAED;;;;;;;;;;;;;;OAcG;IACH,kCALW,YAAY,GAAC,YAAY,eACzB,MAAM,EAAE,EAAE,eACV,MAAM,GACJ;QAAC,IAAI,EAAE,YAAY,CAAC;QAAC,IAAI,EAAE,MAAM,EAAE,CAAA;KAAC,CAoBhD;IAGD;;;;OAIG;IACH,aAHW,YAAY,GAAC,YAAY;;QACvB,QAAQ;QAAE,cAAc,EAAE,MAAM,CAAA;KAAE,CAAC,CAsB/C;CACJ;AAID;CAAkE;;;;;AAElE;;;GAGG;AACH;IACI;;;OAGG;IACH,+BAFW,gBAAgB,EAM1B;IAFG,oCAA0C;IAI9C;;;;;OAKG;IACH,aAJW,GAAG,WACA,GAAG,KACJ,QAAQ,GAAG,CAAC,CAIxB;CACJ;AAED;IACI;;OAEG;IACH,oCAEC;IAED;;OAEG;IACH,wCAGC;IACD;;OAEG;IACH,0CAGC;CACJ;AAED;;;GAGG;AACH;IACI;;;;OAIG;IACH,aAHW,GAAG,GACD,QAAQ,GAAG,CAAC,CAIxB;CACJ;AAGD;IACI;;;;OAIG;IACH,aAHW,GAAG,GACD,QAAQ,GAAG,CAAC,CAIxB;CACJ;AAED;IACI;;;;OAIG;IACH,aAHW,GAAG,GACD,QAAQ,GAAG,CAAC,CAIxB;CACJ;AAED;CAAkD;AAIlD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6BG;AACH;IACI;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA6BC;IAED;;;;;;MAMC;IAED;;;;;;;;;;;;;;OAcG;IACH,sDATW,MAAM,0EAKN,OAAO,gBAAgB,EAAE,iBAAiB,GAExC,QAAQ,SAAS,CAAC,CAuC9B;CACJ;;;;;0BAvxDY,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;;;;;kBAqC9B,MAAM;;;;oBACN,aAAa;;;;0BACb,aAAa;;;gBA2lBb,MAAM;;yCACP,2BAA2B,GAAG,+BAA+B;;kBA0U5D,MAAM;oBACN,aAAa;0BACb,aAAa;mBACb,MAAM;mBACN,MAAM;;yBA7jCK,kBAAkB;uBAFgB,mBAAmB"}