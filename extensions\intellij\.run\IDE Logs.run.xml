<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="IDE Logs" type="ShConfigurationType">
    <option name="SCRIPT_TEXT" value="mkdir -p ./extensions/intellij/build/idea-sandbox/system/log &amp;&amp; touch ./extensions/intellij/build/idea-sandbox/system/log/idea.log &amp;&amp; tail -f ./extensions/intellij/build/idea-sandbox/system/log/idea.log" />
    <option name="INDEPENDENT_SCRIPT_PATH" value="true" />
    <option name="SCRIPT_PATH" value="mkdir -p ./extensions/.continue-debug/logs &amp;&amp; touch ./extensions/.continue-debug/logs/prompt.log &amp;&amp; tail -f ./extensions/.continue-debug/logs/prompt.log" />
    <option name="SCRIPT_OPTIONS" value="" />
    <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true" />
    <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$" />
    <option name="INDEPENDENT_INTERPRETER_PATH" value="true" />
    <option name="INTERPRETER_PATH" value="/bin/zsh" />
    <option name="INTERPRETER_OPTIONS" value="" />
    <option name="EXECUTE_IN_TERMINAL" value="false" />
    <option name="EXECUTE_SCRIPT_FILE" value="false" />
    <envs />
    <method v="2" />
  </configuration>
</component>