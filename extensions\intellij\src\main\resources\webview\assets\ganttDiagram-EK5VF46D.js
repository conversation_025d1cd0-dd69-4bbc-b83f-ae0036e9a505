import{a_ as nn,a$ as Ln,b0 as rn,b1 as an,b2 as sn,b3 as se,b4 as An,_ as d,g as In,s as Wn,q as Hn,p as On,a as Nn,b as Vn,c as _t,d as Bt,e as Pn,b5 as it,l as Kt,k as zn,j as Rn,y as qn,u as Bn}from"./index.js";import{g as we}from"./XCircleIcon.js";import{b as Zn,t as He,c as Xn,a as Gn,l as Qn}from"./linear.js";import{i as $n}from"./init.js";import"./defaultLocale.js";function jn(t,e){let n;if(e===void 0)for(const r of t)r!=null&&(n<r||n===void 0&&r>=r)&&(n=r);else{let r=-1;for(let i of t)(i=e(i,++r,t))!=null&&(n<i||n===void 0&&i>=i)&&(n=i)}return n}function Jn(t,e){let n;if(e===void 0)for(const r of t)r!=null&&(n>r||n===void 0&&r>=r)&&(n=r);else{let r=-1;for(let i of t)(i=e(i,++r,t))!=null&&(n>i||n===void 0&&i>=i)&&(n=i)}return n}function Kn(t){return t}var Xt=1,oe=2,ke=3,Zt=4,Oe=1e-6;function tr(t){return"translate("+t+",0)"}function er(t){return"translate(0,"+t+")"}function nr(t){return e=>+t(e)}function rr(t,e){return e=Math.max(0,t.bandwidth()-e*2)/2,t.round()&&(e=Math.round(e)),n=>+t(n)+e}function ir(){return!this.__axis}function on(t,e){var n=[],r=null,i=null,a=6,s=6,p=3,M=typeof window<"u"&&window.devicePixelRatio>1?0:.5,T=t===Xt||t===Zt?-1:1,g=t===Zt||t===oe?"x":"y",U=t===Xt||t===ke?tr:er;function C(b){var X=r??(e.ticks?e.ticks.apply(e,n):e.domain()),H=i??(e.tickFormat?e.tickFormat.apply(e,n):Kn),D=Math.max(a,0)+p,I=e.range(),V=+I[0]+M,W=+I[I.length-1]+M,B=(e.bandwidth?rr:nr)(e.copy(),M),$=b.selection?b.selection():b,w=$.selectAll(".domain").data([null]),O=$.selectAll(".tick").data(X,e).order(),x=O.exit(),F=O.enter().append("g").attr("class","tick"),S=O.select("line"),_=O.select("text");w=w.merge(w.enter().insert("path",".tick").attr("class","domain").attr("stroke","currentColor")),O=O.merge(F),S=S.merge(F.append("line").attr("stroke","currentColor").attr(g+"2",T*a)),_=_.merge(F.append("text").attr("fill","currentColor").attr(g,T*D).attr("dy",t===Xt?"0em":t===ke?"0.71em":"0.32em")),b!==$&&(w=w.transition(b),O=O.transition(b),S=S.transition(b),_=_.transition(b),x=x.transition(b).attr("opacity",Oe).attr("transform",function(k){return isFinite(k=B(k))?U(k+M):this.getAttribute("transform")}),F.attr("opacity",Oe).attr("transform",function(k){var Y=this.parentNode.__axis;return U((Y&&isFinite(Y=Y(k))?Y:B(k))+M)})),x.remove(),w.attr("d",t===Zt||t===oe?s?"M"+T*s+","+V+"H"+M+"V"+W+"H"+T*s:"M"+M+","+V+"V"+W:s?"M"+V+","+T*s+"V"+M+"H"+W+"V"+T*s:"M"+V+","+M+"H"+W),O.attr("opacity",1).attr("transform",function(k){return U(B(k)+M)}),S.attr(g+"2",T*a),_.attr(g,T*D).text(H),$.filter(ir).attr("fill","none").attr("font-size",10).attr("font-family","sans-serif").attr("text-anchor",t===oe?"start":t===Zt?"end":"middle"),$.each(function(){this.__axis=B})}return C.scale=function(b){return arguments.length?(e=b,C):e},C.ticks=function(){return n=Array.from(arguments),C},C.tickArguments=function(b){return arguments.length?(n=b==null?[]:Array.from(b),C):n.slice()},C.tickValues=function(b){return arguments.length?(r=b==null?null:Array.from(b),C):r&&r.slice()},C.tickFormat=function(b){return arguments.length?(i=b,C):i},C.tickSize=function(b){return arguments.length?(a=s=+b,C):a},C.tickSizeInner=function(b){return arguments.length?(a=+b,C):a},C.tickSizeOuter=function(b){return arguments.length?(s=+b,C):s},C.tickPadding=function(b){return arguments.length?(p=+b,C):p},C.offset=function(b){return arguments.length?(M=+b,C):M},C}function ar(t){return on(Xt,t)}function sr(t){return on(ke,t)}const or=Math.PI/180,cr=180/Math.PI,te=18,cn=.96422,un=1,ln=.82521,fn=4/29,St=6/29,hn=3*St*St,ur=St*St*St;function dn(t){if(t instanceof ft)return new ft(t.l,t.a,t.b,t.opacity);if(t instanceof dt)return mn(t);t instanceof nn||(t=Ln(t));var e=fe(t.r),n=fe(t.g),r=fe(t.b),i=ce((.2225045*e+.7168786*n+.0606169*r)/un),a,s;return e===n&&n===r?a=s=i:(a=ce((.4360747*e+.3850649*n+.1430804*r)/cn),s=ce((.0139322*e+.0971045*n+.7141733*r)/ln)),new ft(116*i-16,500*(a-i),200*(i-s),t.opacity)}function lr(t,e,n,r){return arguments.length===1?dn(t):new ft(t,e,n,r??1)}function ft(t,e,n,r){this.l=+t,this.a=+e,this.b=+n,this.opacity=+r}rn(ft,lr,an(sn,{brighter(t){return new ft(this.l+te*(t??1),this.a,this.b,this.opacity)},darker(t){return new ft(this.l-te*(t??1),this.a,this.b,this.opacity)},rgb(){var t=(this.l+16)/116,e=isNaN(this.a)?t:t+this.a/500,n=isNaN(this.b)?t:t-this.b/200;return e=cn*ue(e),t=un*ue(t),n=ln*ue(n),new nn(le(3.1338561*e-1.6168667*t-.4906146*n),le(-.9787684*e+1.9161415*t+.033454*n),le(.0719453*e-.2289914*t+1.4052427*n),this.opacity)}}));function ce(t){return t>ur?Math.pow(t,1/3):t/hn+fn}function ue(t){return t>St?t*t*t:hn*(t-fn)}function le(t){return 255*(t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055)}function fe(t){return(t/=255)<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function fr(t){if(t instanceof dt)return new dt(t.h,t.c,t.l,t.opacity);if(t instanceof ft||(t=dn(t)),t.a===0&&t.b===0)return new dt(NaN,0<t.l&&t.l<100?0:NaN,t.l,t.opacity);var e=Math.atan2(t.b,t.a)*cr;return new dt(e<0?e+360:e,Math.sqrt(t.a*t.a+t.b*t.b),t.l,t.opacity)}function pe(t,e,n,r){return arguments.length===1?fr(t):new dt(t,e,n,r??1)}function dt(t,e,n,r){this.h=+t,this.c=+e,this.l=+n,this.opacity=+r}function mn(t){if(isNaN(t.h))return new ft(t.l,0,0,t.opacity);var e=t.h*or;return new ft(t.l,Math.cos(e)*t.c,Math.sin(e)*t.c,t.opacity)}rn(dt,pe,an(sn,{brighter(t){return new dt(this.h,this.c,this.l+te*(t??1),this.opacity)},darker(t){return new dt(this.h,this.c,this.l-te*(t??1),this.opacity)},rgb(){return mn(this).rgb()}}));function hr(t){return function(e,n){var r=t((e=pe(e)).h,(n=pe(n)).h),i=se(e.c,n.c),a=se(e.l,n.l),s=se(e.opacity,n.opacity);return function(p){return e.h=r(p),e.c=i(p),e.l=a(p),e.opacity=s(p),e+""}}}const dr=hr(An);function mr(t,e){t=t.slice();var n=0,r=t.length-1,i=t[n],a=t[r],s;return a<i&&(s=n,n=r,r=s,s=i,i=a,a=s),t[n]=e.floor(i),t[r]=e.ceil(a),t}const he=new Date,de=new Date;function et(t,e,n,r){function i(a){return t(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=a=>(t(a=new Date(+a)),a),i.ceil=a=>(t(a=new Date(a-1)),e(a,1),t(a),a),i.round=a=>{const s=i(a),p=i.ceil(a);return a-s<p-a?s:p},i.offset=(a,s)=>(e(a=new Date(+a),s==null?1:Math.floor(s)),a),i.range=(a,s,p)=>{const M=[];if(a=i.ceil(a),p=p==null?1:Math.floor(p),!(a<s)||!(p>0))return M;let T;do M.push(T=new Date(+a)),e(a,p),t(a);while(T<a&&a<s);return M},i.filter=a=>et(s=>{if(s>=s)for(;t(s),!a(s);)s.setTime(s-1)},(s,p)=>{if(s>=s)if(p<0)for(;++p<=0;)for(;e(s,-1),!a(s););else for(;--p>=0;)for(;e(s,1),!a(s););}),n&&(i.count=(a,s)=>(he.setTime(+a),de.setTime(+s),t(he),t(de),Math.floor(n(he,de))),i.every=a=>(a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(r?s=>r(s)%a===0:s=>i.count(0,s)%a===0):i)),i}const Yt=et(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);Yt.every=t=>(t=Math.floor(t),!isFinite(t)||!(t>0)?null:t>1?et(e=>{e.setTime(Math.floor(e/t)*t)},(e,n)=>{e.setTime(+e+n*t)},(e,n)=>(n-e)/t):Yt);Yt.range;const mt=1e3,ct=mt*60,gt=ct*60,yt=gt*24,Ce=yt*7,Ne=yt*30,me=yt*365,vt=et(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+e*mt)},(t,e)=>(e-t)/mt,t=>t.getUTCSeconds());vt.range;const Wt=et(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*mt)},(t,e)=>{t.setTime(+t+e*ct)},(t,e)=>(e-t)/ct,t=>t.getMinutes());Wt.range;const gr=et(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+e*ct)},(t,e)=>(e-t)/ct,t=>t.getUTCMinutes());gr.range;const Ht=et(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*mt-t.getMinutes()*ct)},(t,e)=>{t.setTime(+t+e*gt)},(t,e)=>(e-t)/gt,t=>t.getHours());Ht.range;const yr=et(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+e*gt)},(t,e)=>(e-t)/gt,t=>t.getUTCHours());yr.range;const Tt=et(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*ct)/yt,t=>t.getDate()-1);Tt.range;const De=et(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/yt,t=>t.getUTCDate()-1);De.range;const kr=et(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/yt,t=>Math.floor(t/yt));kr.range;function wt(t){return et(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(e,n)=>{e.setDate(e.getDate()+n*7)},(e,n)=>(n-e-(n.getTimezoneOffset()-e.getTimezoneOffset())*ct)/Ce)}const Vt=wt(0),Ot=wt(1),gn=wt(2),yn=wt(3),bt=wt(4),kn=wt(5),pn=wt(6);Vt.range;Ot.range;gn.range;yn.range;bt.range;kn.range;pn.range;function Ct(t){return et(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(e,n)=>{e.setUTCDate(e.getUTCDate()+n*7)},(e,n)=>(n-e)/Ce)}const vn=Ct(0),ee=Ct(1),pr=Ct(2),vr=Ct(3),Ut=Ct(4),Tr=Ct(5),br=Ct(6);vn.range;ee.range;pr.range;vr.range;Ut.range;Tr.range;br.range;const Nt=et(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());Nt.range;const xr=et(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());xr.range;const kt=et(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());kt.every=t=>!isFinite(t=Math.floor(t))||!(t>0)?null:et(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,n)=>{e.setFullYear(e.getFullYear()+n*t)});kt.range;const xt=et(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());xt.every=t=>!isFinite(t=Math.floor(t))||!(t>0)?null:et(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,n)=>{e.setUTCFullYear(e.getUTCFullYear()+n*t)});xt.range;function wr(t,e,n,r,i,a){const s=[[vt,1,mt],[vt,5,5*mt],[vt,15,15*mt],[vt,30,30*mt],[a,1,ct],[a,5,5*ct],[a,15,15*ct],[a,30,30*ct],[i,1,gt],[i,3,3*gt],[i,6,6*gt],[i,12,12*gt],[r,1,yt],[r,2,2*yt],[n,1,Ce],[e,1,Ne],[e,3,3*Ne],[t,1,me]];function p(T,g,U){const C=g<T;C&&([T,g]=[g,T]);const b=U&&typeof U.range=="function"?U:M(T,g,U),X=b?b.range(T,+g+1):[];return C?X.reverse():X}function M(T,g,U){const C=Math.abs(g-T)/U,b=Zn(([,,D])=>D).right(s,C);if(b===s.length)return t.every(He(T/me,g/me,U));if(b===0)return Yt.every(Math.max(He(T,g,U),1));const[X,H]=s[C/s[b-1][2]<s[b][2]/C?b-1:b];return X.every(H)}return[p,M]}const[Cr,Dr]=wr(kt,Nt,Vt,Tt,Ht,Wt);function ge(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function ye(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function Lt(t,e,n){return{y:t,m:e,d:n,H:0,M:0,S:0,L:0}}function Mr(t){var e=t.dateTime,n=t.date,r=t.time,i=t.periods,a=t.days,s=t.shortDays,p=t.months,M=t.shortMonths,T=At(i),g=It(i),U=At(a),C=It(a),b=At(s),X=It(s),H=At(p),D=It(p),I=At(M),V=It(M),W={a:m,A:E,b:c,B:u,c:null,d:Be,e:Be,f:Qr,g:ai,G:oi,H:Zr,I:Xr,j:Gr,L:Tn,m:$r,M:jr,p:o,q:R,Q:Ge,s:Qe,S:Jr,u:Kr,U:ti,V:ei,w:ni,W:ri,x:null,X:null,y:ii,Y:si,Z:ci,"%":Xe},B={a:P,A:z,b:K,B:G,c:null,d:Ze,e:Ze,f:hi,g:xi,G:Ci,H:ui,I:li,j:fi,L:xn,m:di,M:mi,p:j,q:at,Q:Ge,s:Qe,S:gi,u:yi,U:ki,V:pi,w:vi,W:Ti,x:null,X:null,y:bi,Y:wi,Z:Di,"%":Xe},$={a:S,A:_,b:k,B:Y,c:l,d:Re,e:Re,f:zr,g:ze,G:Pe,H:qe,I:qe,j:Or,L:Pr,m:Hr,M:Nr,p:F,q:Wr,Q:qr,s:Br,S:Vr,u:Ur,U:Er,V:Lr,w:Yr,W:Ar,x:h,X:y,y:ze,Y:Pe,Z:Ir,"%":Rr};W.x=w(n,W),W.X=w(r,W),W.c=w(e,W),B.x=w(n,B),B.X=w(r,B),B.c=w(e,B);function w(v,A){return function(N){var f=[],J=-1,L=0,Q=v.length,Z,rt,st;for(N instanceof Date||(N=new Date(+N));++J<Q;)v.charCodeAt(J)===37&&(f.push(v.slice(L,J)),(rt=Ve[Z=v.charAt(++J)])!=null?Z=v.charAt(++J):rt=Z==="e"?" ":"0",(st=A[Z])&&(Z=st(N,rt)),f.push(Z),L=J+1);return f.push(v.slice(L,J)),f.join("")}}function O(v,A){return function(N){var f=Lt(1900,void 0,1),J=x(f,v,N+="",0),L,Q;if(J!=N.length)return null;if("Q"in f)return new Date(f.Q);if("s"in f)return new Date(f.s*1e3+("L"in f?f.L:0));if(A&&!("Z"in f)&&(f.Z=0),"p"in f&&(f.H=f.H%12+f.p*12),f.m===void 0&&(f.m="q"in f?f.q:0),"V"in f){if(f.V<1||f.V>53)return null;"w"in f||(f.w=1),"Z"in f?(L=ye(Lt(f.y,0,1)),Q=L.getUTCDay(),L=Q>4||Q===0?ee.ceil(L):ee(L),L=De.offset(L,(f.V-1)*7),f.y=L.getUTCFullYear(),f.m=L.getUTCMonth(),f.d=L.getUTCDate()+(f.w+6)%7):(L=ge(Lt(f.y,0,1)),Q=L.getDay(),L=Q>4||Q===0?Ot.ceil(L):Ot(L),L=Tt.offset(L,(f.V-1)*7),f.y=L.getFullYear(),f.m=L.getMonth(),f.d=L.getDate()+(f.w+6)%7)}else("W"in f||"U"in f)&&("w"in f||(f.w="u"in f?f.u%7:"W"in f?1:0),Q="Z"in f?ye(Lt(f.y,0,1)).getUTCDay():ge(Lt(f.y,0,1)).getDay(),f.m=0,f.d="W"in f?(f.w+6)%7+f.W*7-(Q+5)%7:f.w+f.U*7-(Q+6)%7);return"Z"in f?(f.H+=f.Z/100|0,f.M+=f.Z%100,ye(f)):ge(f)}}function x(v,A,N,f){for(var J=0,L=A.length,Q=N.length,Z,rt;J<L;){if(f>=Q)return-1;if(Z=A.charCodeAt(J++),Z===37){if(Z=A.charAt(J++),rt=$[Z in Ve?A.charAt(J++):Z],!rt||(f=rt(v,N,f))<0)return-1}else if(Z!=N.charCodeAt(f++))return-1}return f}function F(v,A,N){var f=T.exec(A.slice(N));return f?(v.p=g.get(f[0].toLowerCase()),N+f[0].length):-1}function S(v,A,N){var f=b.exec(A.slice(N));return f?(v.w=X.get(f[0].toLowerCase()),N+f[0].length):-1}function _(v,A,N){var f=U.exec(A.slice(N));return f?(v.w=C.get(f[0].toLowerCase()),N+f[0].length):-1}function k(v,A,N){var f=I.exec(A.slice(N));return f?(v.m=V.get(f[0].toLowerCase()),N+f[0].length):-1}function Y(v,A,N){var f=H.exec(A.slice(N));return f?(v.m=D.get(f[0].toLowerCase()),N+f[0].length):-1}function l(v,A,N){return x(v,e,A,N)}function h(v,A,N){return x(v,n,A,N)}function y(v,A,N){return x(v,r,A,N)}function m(v){return s[v.getDay()]}function E(v){return a[v.getDay()]}function c(v){return M[v.getMonth()]}function u(v){return p[v.getMonth()]}function o(v){return i[+(v.getHours()>=12)]}function R(v){return 1+~~(v.getMonth()/3)}function P(v){return s[v.getUTCDay()]}function z(v){return a[v.getUTCDay()]}function K(v){return M[v.getUTCMonth()]}function G(v){return p[v.getUTCMonth()]}function j(v){return i[+(v.getUTCHours()>=12)]}function at(v){return 1+~~(v.getUTCMonth()/3)}return{format:function(v){var A=w(v+="",W);return A.toString=function(){return v},A},parse:function(v){var A=O(v+="",!1);return A.toString=function(){return v},A},utcFormat:function(v){var A=w(v+="",B);return A.toString=function(){return v},A},utcParse:function(v){var A=O(v+="",!0);return A.toString=function(){return v},A}}}var Ve={"-":"",_:" ",0:"0"},nt=/^\s*\d+/,_r=/^%/,Sr=/[\\^$*+?|[\]().{}]/g;function q(t,e,n){var r=t<0?"-":"",i=(r?-t:t)+"",a=i.length;return r+(a<n?new Array(n-a+1).join(e)+i:i)}function Fr(t){return t.replace(Sr,"\\$&")}function At(t){return new RegExp("^(?:"+t.map(Fr).join("|")+")","i")}function It(t){return new Map(t.map((e,n)=>[e.toLowerCase(),n]))}function Yr(t,e,n){var r=nt.exec(e.slice(n,n+1));return r?(t.w=+r[0],n+r[0].length):-1}function Ur(t,e,n){var r=nt.exec(e.slice(n,n+1));return r?(t.u=+r[0],n+r[0].length):-1}function Er(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.U=+r[0],n+r[0].length):-1}function Lr(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.V=+r[0],n+r[0].length):-1}function Ar(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.W=+r[0],n+r[0].length):-1}function Pe(t,e,n){var r=nt.exec(e.slice(n,n+4));return r?(t.y=+r[0],n+r[0].length):-1}function ze(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.y=+r[0]+(+r[0]>68?1900:2e3),n+r[0].length):-1}function Ir(t,e,n){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(n,n+6));return r?(t.Z=r[1]?0:-(r[2]+(r[3]||"00")),n+r[0].length):-1}function Wr(t,e,n){var r=nt.exec(e.slice(n,n+1));return r?(t.q=r[0]*3-3,n+r[0].length):-1}function Hr(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.m=r[0]-1,n+r[0].length):-1}function Re(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.d=+r[0],n+r[0].length):-1}function Or(t,e,n){var r=nt.exec(e.slice(n,n+3));return r?(t.m=0,t.d=+r[0],n+r[0].length):-1}function qe(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.H=+r[0],n+r[0].length):-1}function Nr(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.M=+r[0],n+r[0].length):-1}function Vr(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.S=+r[0],n+r[0].length):-1}function Pr(t,e,n){var r=nt.exec(e.slice(n,n+3));return r?(t.L=+r[0],n+r[0].length):-1}function zr(t,e,n){var r=nt.exec(e.slice(n,n+6));return r?(t.L=Math.floor(r[0]/1e3),n+r[0].length):-1}function Rr(t,e,n){var r=_r.exec(e.slice(n,n+1));return r?n+r[0].length:-1}function qr(t,e,n){var r=nt.exec(e.slice(n));return r?(t.Q=+r[0],n+r[0].length):-1}function Br(t,e,n){var r=nt.exec(e.slice(n));return r?(t.s=+r[0],n+r[0].length):-1}function Be(t,e){return q(t.getDate(),e,2)}function Zr(t,e){return q(t.getHours(),e,2)}function Xr(t,e){return q(t.getHours()%12||12,e,2)}function Gr(t,e){return q(1+Tt.count(kt(t),t),e,3)}function Tn(t,e){return q(t.getMilliseconds(),e,3)}function Qr(t,e){return Tn(t,e)+"000"}function $r(t,e){return q(t.getMonth()+1,e,2)}function jr(t,e){return q(t.getMinutes(),e,2)}function Jr(t,e){return q(t.getSeconds(),e,2)}function Kr(t){var e=t.getDay();return e===0?7:e}function ti(t,e){return q(Vt.count(kt(t)-1,t),e,2)}function bn(t){var e=t.getDay();return e>=4||e===0?bt(t):bt.ceil(t)}function ei(t,e){return t=bn(t),q(bt.count(kt(t),t)+(kt(t).getDay()===4),e,2)}function ni(t){return t.getDay()}function ri(t,e){return q(Ot.count(kt(t)-1,t),e,2)}function ii(t,e){return q(t.getFullYear()%100,e,2)}function ai(t,e){return t=bn(t),q(t.getFullYear()%100,e,2)}function si(t,e){return q(t.getFullYear()%1e4,e,4)}function oi(t,e){var n=t.getDay();return t=n>=4||n===0?bt(t):bt.ceil(t),q(t.getFullYear()%1e4,e,4)}function ci(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+q(e/60|0,"0",2)+q(e%60,"0",2)}function Ze(t,e){return q(t.getUTCDate(),e,2)}function ui(t,e){return q(t.getUTCHours(),e,2)}function li(t,e){return q(t.getUTCHours()%12||12,e,2)}function fi(t,e){return q(1+De.count(xt(t),t),e,3)}function xn(t,e){return q(t.getUTCMilliseconds(),e,3)}function hi(t,e){return xn(t,e)+"000"}function di(t,e){return q(t.getUTCMonth()+1,e,2)}function mi(t,e){return q(t.getUTCMinutes(),e,2)}function gi(t,e){return q(t.getUTCSeconds(),e,2)}function yi(t){var e=t.getUTCDay();return e===0?7:e}function ki(t,e){return q(vn.count(xt(t)-1,t),e,2)}function wn(t){var e=t.getUTCDay();return e>=4||e===0?Ut(t):Ut.ceil(t)}function pi(t,e){return t=wn(t),q(Ut.count(xt(t),t)+(xt(t).getUTCDay()===4),e,2)}function vi(t){return t.getUTCDay()}function Ti(t,e){return q(ee.count(xt(t)-1,t),e,2)}function bi(t,e){return q(t.getUTCFullYear()%100,e,2)}function xi(t,e){return t=wn(t),q(t.getUTCFullYear()%100,e,2)}function wi(t,e){return q(t.getUTCFullYear()%1e4,e,4)}function Ci(t,e){var n=t.getUTCDay();return t=n>=4||n===0?Ut(t):Ut.ceil(t),q(t.getUTCFullYear()%1e4,e,4)}function Di(){return"+0000"}function Xe(){return"%"}function Ge(t){return+t}function Qe(t){return Math.floor(+t/1e3)}var Mt,ne;Mi({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function Mi(t){return Mt=Mr(t),ne=Mt.format,Mt.parse,Mt.utcFormat,Mt.utcParse,Mt}function _i(t){return new Date(t)}function Si(t){return t instanceof Date?+t:+new Date(+t)}function Cn(t,e,n,r,i,a,s,p,M,T){var g=Xn(),U=g.invert,C=g.domain,b=T(".%L"),X=T(":%S"),H=T("%I:%M"),D=T("%I %p"),I=T("%a %d"),V=T("%b %d"),W=T("%B"),B=T("%Y");function $(w){return(M(w)<w?b:p(w)<w?X:s(w)<w?H:a(w)<w?D:r(w)<w?i(w)<w?I:V:n(w)<w?W:B)(w)}return g.invert=function(w){return new Date(U(w))},g.domain=function(w){return arguments.length?C(Array.from(w,Si)):C().map(_i)},g.ticks=function(w){var O=C();return t(O[0],O[O.length-1],w??10)},g.tickFormat=function(w,O){return O==null?$:T(O)},g.nice=function(w){var O=C();return(!w||typeof w.range!="function")&&(w=e(O[0],O[O.length-1],w??10)),w?C(mr(O,w)):g},g.copy=function(){return Gn(g,Cn(t,e,n,r,i,a,s,p,M,T))},g}function Fi(){return $n.apply(Cn(Cr,Dr,kt,Nt,Vt,Tt,Ht,Wt,vt,ne).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}var Gt={exports:{}},Yi=Gt.exports,$e;function Ui(){return $e||($e=1,function(t,e){(function(n,r){t.exports=r()})(Yi,function(){var n="day";return function(r,i,a){var s=function(T){return T.add(4-T.isoWeekday(),n)},p=i.prototype;p.isoWeekYear=function(){return s(this).year()},p.isoWeek=function(T){if(!this.$utils().u(T))return this.add(7*(T-this.isoWeek()),n);var g,U,C,b,X=s(this),H=(g=this.isoWeekYear(),U=this.$u,C=(U?a.utc:a)().year(g).startOf("year"),b=4-C.isoWeekday(),C.isoWeekday()>4&&(b+=7),C.add(b,n));return X.diff(H,"week")+1},p.isoWeekday=function(T){return this.$utils().u(T)?this.day()||7:this.day(this.day()%7?T:T-7)};var M=p.startOf;p.startOf=function(T,g){var U=this.$utils(),C=!!U.u(g)||g;return U.p(T)==="isoweek"?C?this.date(this.date()-(this.isoWeekday()-1)).startOf("day"):this.date(this.date()-1-(this.isoWeekday()-1)+7).endOf("day"):M.bind(this)(T,g)}}})}(Gt)),Gt.exports}var Ei=Ui();const Li=we(Ei);var Qt={exports:{}},Ai=Qt.exports,je;function Ii(){return je||(je=1,function(t,e){(function(n,r){t.exports=r()})(Ai,function(){var n={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},r=/(\[[^[]*\])|([-_:/.,()\s]+)|(A|a|Q|YYYY|YY?|ww?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g,i=/\d/,a=/\d\d/,s=/\d\d?/,p=/\d*[^-_:/,()\s\d]+/,M={},T=function(D){return(D=+D)+(D>68?1900:2e3)},g=function(D){return function(I){this[D]=+I}},U=[/[+-]\d\d:?(\d\d)?|Z/,function(D){(this.zone||(this.zone={})).offset=function(I){if(!I||I==="Z")return 0;var V=I.match(/([+-]|\d\d)/g),W=60*V[1]+(+V[2]||0);return W===0?0:V[0]==="+"?-W:W}(D)}],C=function(D){var I=M[D];return I&&(I.indexOf?I:I.s.concat(I.f))},b=function(D,I){var V,W=M.meridiem;if(W){for(var B=1;B<=24;B+=1)if(D.indexOf(W(B,0,I))>-1){V=B>12;break}}else V=D===(I?"pm":"PM");return V},X={A:[p,function(D){this.afternoon=b(D,!1)}],a:[p,function(D){this.afternoon=b(D,!0)}],Q:[i,function(D){this.month=3*(D-1)+1}],S:[i,function(D){this.milliseconds=100*+D}],SS:[a,function(D){this.milliseconds=10*+D}],SSS:[/\d{3}/,function(D){this.milliseconds=+D}],s:[s,g("seconds")],ss:[s,g("seconds")],m:[s,g("minutes")],mm:[s,g("minutes")],H:[s,g("hours")],h:[s,g("hours")],HH:[s,g("hours")],hh:[s,g("hours")],D:[s,g("day")],DD:[a,g("day")],Do:[p,function(D){var I=M.ordinal,V=D.match(/\d+/);if(this.day=V[0],I)for(var W=1;W<=31;W+=1)I(W).replace(/\[|\]/g,"")===D&&(this.day=W)}],w:[s,g("week")],ww:[a,g("week")],M:[s,g("month")],MM:[a,g("month")],MMM:[p,function(D){var I=C("months"),V=(C("monthsShort")||I.map(function(W){return W.slice(0,3)})).indexOf(D)+1;if(V<1)throw new Error;this.month=V%12||V}],MMMM:[p,function(D){var I=C("months").indexOf(D)+1;if(I<1)throw new Error;this.month=I%12||I}],Y:[/[+-]?\d+/,g("year")],YY:[a,function(D){this.year=T(D)}],YYYY:[/\d{4}/,g("year")],Z:U,ZZ:U};function H(D){var I,V;I=D,V=M&&M.formats;for(var W=(D=I.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,function(S,_,k){var Y=k&&k.toUpperCase();return _||V[k]||n[k]||V[Y].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(l,h,y){return h||y.slice(1)})})).match(r),B=W.length,$=0;$<B;$+=1){var w=W[$],O=X[w],x=O&&O[0],F=O&&O[1];W[$]=F?{regex:x,parser:F}:w.replace(/^\[|\]$/g,"")}return function(S){for(var _={},k=0,Y=0;k<B;k+=1){var l=W[k];if(typeof l=="string")Y+=l.length;else{var h=l.regex,y=l.parser,m=S.slice(Y),E=h.exec(m)[0];y.call(_,E),S=S.replace(E,"")}}return function(c){var u=c.afternoon;if(u!==void 0){var o=c.hours;u?o<12&&(c.hours+=12):o===12&&(c.hours=0),delete c.afternoon}}(_),_}}return function(D,I,V){V.p.customParseFormat=!0,D&&D.parseTwoDigitYear&&(T=D.parseTwoDigitYear);var W=I.prototype,B=W.parse;W.parse=function($){var w=$.date,O=$.utc,x=$.args;this.$u=O;var F=x[1];if(typeof F=="string"){var S=x[2]===!0,_=x[3]===!0,k=S||_,Y=x[2];_&&(Y=x[2]),M=this.$locale(),!S&&Y&&(M=V.Ls[Y]),this.$d=function(m,E,c,u){try{if(["x","X"].indexOf(E)>-1)return new Date((E==="X"?1e3:1)*m);var o=H(E)(m),R=o.year,P=o.month,z=o.day,K=o.hours,G=o.minutes,j=o.seconds,at=o.milliseconds,v=o.zone,A=o.week,N=new Date,f=z||(R||P?1:N.getDate()),J=R||N.getFullYear(),L=0;R&&!P||(L=P>0?P-1:N.getMonth());var Q,Z=K||0,rt=G||0,st=j||0,pt=at||0;return v?new Date(Date.UTC(J,L,f,Z,rt,st,pt+60*v.offset*1e3)):c?new Date(Date.UTC(J,L,f,Z,rt,st,pt)):(Q=new Date(J,L,f,Z,rt,st,pt),A&&(Q=u(Q).week(A).toDate()),Q)}catch{return new Date("")}}(w,F,O,V),this.init(),Y&&Y!==!0&&(this.$L=this.locale(Y).$L),k&&w!=this.format(F)&&(this.$d=new Date("")),M={}}else if(F instanceof Array)for(var l=F.length,h=1;h<=l;h+=1){x[1]=F[h-1];var y=V.apply(this,x);if(y.isValid()){this.$d=y.$d,this.$L=y.$L,this.init();break}h===l&&(this.$d=new Date(""))}else B.call(this,$)}}})}(Qt)),Qt.exports}var Wi=Ii();const Hi=we(Wi);var $t={exports:{}},Oi=$t.exports,Je;function Ni(){return Je||(Je=1,function(t,e){(function(n,r){t.exports=r()})(Oi,function(){return function(n,r){var i=r.prototype,a=i.format;i.format=function(s){var p=this,M=this.$locale();if(!this.isValid())return a.bind(this)(s);var T=this.$utils(),g=(s||"YYYY-MM-DDTHH:mm:ssZ").replace(/\[([^\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|Do|X|x|k{1,2}|S/g,function(U){switch(U){case"Q":return Math.ceil((p.$M+1)/3);case"Do":return M.ordinal(p.$D);case"gggg":return p.weekYear();case"GGGG":return p.isoWeekYear();case"wo":return M.ordinal(p.week(),"W");case"w":case"ww":return T.s(p.week(),U==="w"?1:2,"0");case"W":case"WW":return T.s(p.isoWeek(),U==="W"?1:2,"0");case"k":case"kk":return T.s(String(p.$H===0?24:p.$H),U==="k"?1:2,"0");case"X":return Math.floor(p.$d.getTime()/1e3);case"x":return p.$d.getTime();case"z":return"["+p.offsetName()+"]";case"zzz":return"["+p.offsetName("long")+"]";default:return U}});return a.bind(this)(g)}}})}($t)),$t.exports}var Vi=Ni();const Pi=we(Vi);var ve=function(){var t=d(function(Y,l,h,y){for(h=h||{},y=Y.length;y--;h[Y[y]]=l);return h},"o"),e=[6,8,10,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28,29,30,31,33,35,36,38,40],n=[1,26],r=[1,27],i=[1,28],a=[1,29],s=[1,30],p=[1,31],M=[1,32],T=[1,33],g=[1,34],U=[1,9],C=[1,10],b=[1,11],X=[1,12],H=[1,13],D=[1,14],I=[1,15],V=[1,16],W=[1,19],B=[1,20],$=[1,21],w=[1,22],O=[1,23],x=[1,25],F=[1,35],S={trace:d(function(){},"trace"),yy:{},symbols_:{error:2,start:3,gantt:4,document:5,EOF:6,line:7,SPACE:8,statement:9,NL:10,weekday:11,weekday_monday:12,weekday_tuesday:13,weekday_wednesday:14,weekday_thursday:15,weekday_friday:16,weekday_saturday:17,weekday_sunday:18,weekend:19,weekend_friday:20,weekend_saturday:21,dateFormat:22,inclusiveEndDates:23,topAxis:24,axisFormat:25,tickInterval:26,excludes:27,includes:28,todayMarker:29,title:30,acc_title:31,acc_title_value:32,acc_descr:33,acc_descr_value:34,acc_descr_multiline_value:35,section:36,clickStatement:37,taskTxt:38,taskData:39,click:40,callbackname:41,callbackargs:42,href:43,clickStatementDebug:44,$accept:0,$end:1},terminals_:{2:"error",4:"gantt",6:"EOF",8:"SPACE",10:"NL",12:"weekday_monday",13:"weekday_tuesday",14:"weekday_wednesday",15:"weekday_thursday",16:"weekday_friday",17:"weekday_saturday",18:"weekday_sunday",20:"weekend_friday",21:"weekend_saturday",22:"dateFormat",23:"inclusiveEndDates",24:"topAxis",25:"axisFormat",26:"tickInterval",27:"excludes",28:"includes",29:"todayMarker",30:"title",31:"acc_title",32:"acc_title_value",33:"acc_descr",34:"acc_descr_value",35:"acc_descr_multiline_value",36:"section",38:"taskTxt",39:"taskData",40:"click",41:"callbackname",42:"callbackargs",43:"href"},productions_:[0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[11,1],[11,1],[11,1],[11,1],[11,1],[11,1],[11,1],[19,1],[19,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,2],[9,2],[9,1],[9,1],[9,1],[9,2],[37,2],[37,3],[37,3],[37,4],[37,3],[37,4],[37,2],[44,2],[44,3],[44,3],[44,4],[44,3],[44,4],[44,2]],performAction:d(function(l,h,y,m,E,c,u){var o=c.length-1;switch(E){case 1:return c[o-1];case 2:this.$=[];break;case 3:c[o-1].push(c[o]),this.$=c[o-1];break;case 4:case 5:this.$=c[o];break;case 6:case 7:this.$=[];break;case 8:m.setWeekday("monday");break;case 9:m.setWeekday("tuesday");break;case 10:m.setWeekday("wednesday");break;case 11:m.setWeekday("thursday");break;case 12:m.setWeekday("friday");break;case 13:m.setWeekday("saturday");break;case 14:m.setWeekday("sunday");break;case 15:m.setWeekend("friday");break;case 16:m.setWeekend("saturday");break;case 17:m.setDateFormat(c[o].substr(11)),this.$=c[o].substr(11);break;case 18:m.enableInclusiveEndDates(),this.$=c[o].substr(18);break;case 19:m.TopAxis(),this.$=c[o].substr(8);break;case 20:m.setAxisFormat(c[o].substr(11)),this.$=c[o].substr(11);break;case 21:m.setTickInterval(c[o].substr(13)),this.$=c[o].substr(13);break;case 22:m.setExcludes(c[o].substr(9)),this.$=c[o].substr(9);break;case 23:m.setIncludes(c[o].substr(9)),this.$=c[o].substr(9);break;case 24:m.setTodayMarker(c[o].substr(12)),this.$=c[o].substr(12);break;case 27:m.setDiagramTitle(c[o].substr(6)),this.$=c[o].substr(6);break;case 28:this.$=c[o].trim(),m.setAccTitle(this.$);break;case 29:case 30:this.$=c[o].trim(),m.setAccDescription(this.$);break;case 31:m.addSection(c[o].substr(8)),this.$=c[o].substr(8);break;case 33:m.addTask(c[o-1],c[o]),this.$="task";break;case 34:this.$=c[o-1],m.setClickEvent(c[o-1],c[o],null);break;case 35:this.$=c[o-2],m.setClickEvent(c[o-2],c[o-1],c[o]);break;case 36:this.$=c[o-2],m.setClickEvent(c[o-2],c[o-1],null),m.setLink(c[o-2],c[o]);break;case 37:this.$=c[o-3],m.setClickEvent(c[o-3],c[o-2],c[o-1]),m.setLink(c[o-3],c[o]);break;case 38:this.$=c[o-2],m.setClickEvent(c[o-2],c[o],null),m.setLink(c[o-2],c[o-1]);break;case 39:this.$=c[o-3],m.setClickEvent(c[o-3],c[o-1],c[o]),m.setLink(c[o-3],c[o-2]);break;case 40:this.$=c[o-1],m.setLink(c[o-1],c[o]);break;case 41:case 47:this.$=c[o-1]+" "+c[o];break;case 42:case 43:case 45:this.$=c[o-2]+" "+c[o-1]+" "+c[o];break;case 44:case 46:this.$=c[o-3]+" "+c[o-2]+" "+c[o-1]+" "+c[o];break}},"anonymous"),table:[{3:1,4:[1,2]},{1:[3]},t(e,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:17,12:n,13:r,14:i,15:a,16:s,17:p,18:M,19:18,20:T,21:g,22:U,23:C,24:b,25:X,26:H,27:D,28:I,29:V,30:W,31:B,33:$,35:w,36:O,37:24,38:x,40:F},t(e,[2,7],{1:[2,1]}),t(e,[2,3]),{9:36,11:17,12:n,13:r,14:i,15:a,16:s,17:p,18:M,19:18,20:T,21:g,22:U,23:C,24:b,25:X,26:H,27:D,28:I,29:V,30:W,31:B,33:$,35:w,36:O,37:24,38:x,40:F},t(e,[2,5]),t(e,[2,6]),t(e,[2,17]),t(e,[2,18]),t(e,[2,19]),t(e,[2,20]),t(e,[2,21]),t(e,[2,22]),t(e,[2,23]),t(e,[2,24]),t(e,[2,25]),t(e,[2,26]),t(e,[2,27]),{32:[1,37]},{34:[1,38]},t(e,[2,30]),t(e,[2,31]),t(e,[2,32]),{39:[1,39]},t(e,[2,8]),t(e,[2,9]),t(e,[2,10]),t(e,[2,11]),t(e,[2,12]),t(e,[2,13]),t(e,[2,14]),t(e,[2,15]),t(e,[2,16]),{41:[1,40],43:[1,41]},t(e,[2,4]),t(e,[2,28]),t(e,[2,29]),t(e,[2,33]),t(e,[2,34],{42:[1,42],43:[1,43]}),t(e,[2,40],{41:[1,44]}),t(e,[2,35],{43:[1,45]}),t(e,[2,36]),t(e,[2,38],{42:[1,46]}),t(e,[2,37]),t(e,[2,39])],defaultActions:{},parseError:d(function(l,h){if(h.recoverable)this.trace(l);else{var y=new Error(l);throw y.hash=h,y}},"parseError"),parse:d(function(l){var h=this,y=[0],m=[],E=[null],c=[],u=this.table,o="",R=0,P=0,z=2,K=1,G=c.slice.call(arguments,1),j=Object.create(this.lexer),at={yy:{}};for(var v in this.yy)Object.prototype.hasOwnProperty.call(this.yy,v)&&(at.yy[v]=this.yy[v]);j.setInput(l,at.yy),at.yy.lexer=j,at.yy.parser=this,typeof j.yylloc>"u"&&(j.yylloc={});var A=j.yylloc;c.push(A);var N=j.options&&j.options.ranges;typeof at.yy.parseError=="function"?this.parseError=at.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function f(ot){y.length=y.length-2*ot,E.length=E.length-ot,c.length=c.length-ot}d(f,"popStack");function J(){var ot;return ot=m.pop()||j.lex()||K,typeof ot!="number"&&(ot instanceof Array&&(m=ot,ot=m.pop()),ot=h.symbols_[ot]||ot),ot}d(J,"lex");for(var L,Q,Z,rt,st={},pt,ut,We,qt;;){if(Q=y[y.length-1],this.defaultActions[Q]?Z=this.defaultActions[Q]:((L===null||typeof L>"u")&&(L=J()),Z=u[Q]&&u[Q][L]),typeof Z>"u"||!Z.length||!Z[0]){var ae="";qt=[];for(pt in u[Q])this.terminals_[pt]&&pt>z&&qt.push("'"+this.terminals_[pt]+"'");j.showPosition?ae="Parse error on line "+(R+1)+`:
`+j.showPosition()+`
Expecting `+qt.join(", ")+", got '"+(this.terminals_[L]||L)+"'":ae="Parse error on line "+(R+1)+": Unexpected "+(L==K?"end of input":"'"+(this.terminals_[L]||L)+"'"),this.parseError(ae,{text:j.match,token:this.terminals_[L]||L,line:j.yylineno,loc:A,expected:qt})}if(Z[0]instanceof Array&&Z.length>1)throw new Error("Parse Error: multiple actions possible at state: "+Q+", token: "+L);switch(Z[0]){case 1:y.push(L),E.push(j.yytext),c.push(j.yylloc),y.push(Z[1]),L=null,P=j.yyleng,o=j.yytext,R=j.yylineno,A=j.yylloc;break;case 2:if(ut=this.productions_[Z[1]][1],st.$=E[E.length-ut],st._$={first_line:c[c.length-(ut||1)].first_line,last_line:c[c.length-1].last_line,first_column:c[c.length-(ut||1)].first_column,last_column:c[c.length-1].last_column},N&&(st._$.range=[c[c.length-(ut||1)].range[0],c[c.length-1].range[1]]),rt=this.performAction.apply(st,[o,P,R,at.yy,Z[1],E,c].concat(G)),typeof rt<"u")return rt;ut&&(y=y.slice(0,-1*ut*2),E=E.slice(0,-1*ut),c=c.slice(0,-1*ut)),y.push(this.productions_[Z[1]][0]),E.push(st.$),c.push(st._$),We=u[y[y.length-2]][y[y.length-1]],y.push(We);break;case 3:return!0}}return!0},"parse")},_=function(){var Y={EOF:1,parseError:d(function(h,y){if(this.yy.parser)this.yy.parser.parseError(h,y);else throw new Error(h)},"parseError"),setInput:d(function(l,h){return this.yy=h||this.yy||{},this._input=l,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:d(function(){var l=this._input[0];this.yytext+=l,this.yyleng++,this.offset++,this.match+=l,this.matched+=l;var h=l.match(/(?:\r\n?|\n).*/g);return h?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),l},"input"),unput:d(function(l){var h=l.length,y=l.split(/(?:\r\n?|\n)/g);this._input=l+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-h),this.offset-=h;var m=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),y.length-1&&(this.yylineno-=y.length-1);var E=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:y?(y.length===m.length?this.yylloc.first_column:0)+m[m.length-y.length].length-y[0].length:this.yylloc.first_column-h},this.options.ranges&&(this.yylloc.range=[E[0],E[0]+this.yyleng-h]),this.yyleng=this.yytext.length,this},"unput"),more:d(function(){return this._more=!0,this},"more"),reject:d(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:d(function(l){this.unput(this.match.slice(l))},"less"),pastInput:d(function(){var l=this.matched.substr(0,this.matched.length-this.match.length);return(l.length>20?"...":"")+l.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:d(function(){var l=this.match;return l.length<20&&(l+=this._input.substr(0,20-l.length)),(l.substr(0,20)+(l.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:d(function(){var l=this.pastInput(),h=new Array(l.length+1).join("-");return l+this.upcomingInput()+`
`+h+"^"},"showPosition"),test_match:d(function(l,h){var y,m,E;if(this.options.backtrack_lexer&&(E={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(E.yylloc.range=this.yylloc.range.slice(0))),m=l[0].match(/(?:\r\n?|\n).*/g),m&&(this.yylineno+=m.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:m?m[m.length-1].length-m[m.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+l[0].length},this.yytext+=l[0],this.match+=l[0],this.matches=l,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(l[0].length),this.matched+=l[0],y=this.performAction.call(this,this.yy,this,h,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),y)return y;if(this._backtrack){for(var c in E)this[c]=E[c];return!1}return!1},"test_match"),next:d(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var l,h,y,m;this._more||(this.yytext="",this.match="");for(var E=this._currentRules(),c=0;c<E.length;c++)if(y=this._input.match(this.rules[E[c]]),y&&(!h||y[0].length>h[0].length)){if(h=y,m=c,this.options.backtrack_lexer){if(l=this.test_match(y,E[c]),l!==!1)return l;if(this._backtrack){h=!1;continue}else return!1}else if(!this.options.flex)break}return h?(l=this.test_match(h,E[m]),l!==!1?l:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:d(function(){var h=this.next();return h||this.lex()},"lex"),begin:d(function(h){this.conditionStack.push(h)},"begin"),popState:d(function(){var h=this.conditionStack.length-1;return h>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:d(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:d(function(h){return h=this.conditionStack.length-1-Math.abs(h||0),h>=0?this.conditionStack[h]:"INITIAL"},"topState"),pushState:d(function(h){this.begin(h)},"pushState"),stateStackSize:d(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:d(function(h,y,m,E){switch(m){case 0:return this.begin("open_directive"),"open_directive";case 1:return this.begin("acc_title"),31;case 2:return this.popState(),"acc_title_value";case 3:return this.begin("acc_descr"),33;case 4:return this.popState(),"acc_descr_value";case 5:this.begin("acc_descr_multiline");break;case 6:this.popState();break;case 7:return"acc_descr_multiline_value";case 8:break;case 9:break;case 10:break;case 11:return 10;case 12:break;case 13:break;case 14:this.begin("href");break;case 15:this.popState();break;case 16:return 43;case 17:this.begin("callbackname");break;case 18:this.popState();break;case 19:this.popState(),this.begin("callbackargs");break;case 20:return 41;case 21:this.popState();break;case 22:return 42;case 23:this.begin("click");break;case 24:this.popState();break;case 25:return 40;case 26:return 4;case 27:return 22;case 28:return 23;case 29:return 24;case 30:return 25;case 31:return 26;case 32:return 28;case 33:return 27;case 34:return 29;case 35:return 12;case 36:return 13;case 37:return 14;case 38:return 15;case 39:return 16;case 40:return 17;case 41:return 18;case 42:return 20;case 43:return 21;case 44:return"date";case 45:return 30;case 46:return"accDescription";case 47:return 36;case 48:return 38;case 49:return 39;case 50:return":";case 51:return 6;case 52:return"INVALID"}},"anonymous"),rules:[/^(?:%%\{)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:%%(?!\{)*[^\n]*)/i,/^(?:[^\}]%%*[^\n]*)/i,/^(?:%%*[^\n]*[\n]*)/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:%[^\n]*)/i,/^(?:href[\s]+["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:call[\s]+)/i,/^(?:\([\s]*\))/i,/^(?:\()/i,/^(?:[^(]*)/i,/^(?:\))/i,/^(?:[^)]*)/i,/^(?:click[\s]+)/i,/^(?:[\s\n])/i,/^(?:[^\s\n]*)/i,/^(?:gantt\b)/i,/^(?:dateFormat\s[^#\n;]+)/i,/^(?:inclusiveEndDates\b)/i,/^(?:topAxis\b)/i,/^(?:axisFormat\s[^#\n;]+)/i,/^(?:tickInterval\s[^#\n;]+)/i,/^(?:includes\s[^#\n;]+)/i,/^(?:excludes\s[^#\n;]+)/i,/^(?:todayMarker\s[^\n;]+)/i,/^(?:weekday\s+monday\b)/i,/^(?:weekday\s+tuesday\b)/i,/^(?:weekday\s+wednesday\b)/i,/^(?:weekday\s+thursday\b)/i,/^(?:weekday\s+friday\b)/i,/^(?:weekday\s+saturday\b)/i,/^(?:weekday\s+sunday\b)/i,/^(?:weekend\s+friday\b)/i,/^(?:weekend\s+saturday\b)/i,/^(?:\d\d\d\d-\d\d-\d\d\b)/i,/^(?:title\s[^\n]+)/i,/^(?:accDescription\s[^#\n;]+)/i,/^(?:section\s[^\n]+)/i,/^(?:[^:\n]+)/i,/^(?::[^#\n;]+)/i,/^(?::)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[6,7],inclusive:!1},acc_descr:{rules:[4],inclusive:!1},acc_title:{rules:[2],inclusive:!1},callbackargs:{rules:[21,22],inclusive:!1},callbackname:{rules:[18,19,20],inclusive:!1},href:{rules:[15,16],inclusive:!1},click:{rules:[24,25],inclusive:!1},INITIAL:{rules:[0,1,3,5,8,9,10,11,12,13,14,17,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52],inclusive:!0}}};return Y}();S.lexer=_;function k(){this.yy={}}return d(k,"Parser"),k.prototype=S,S.Parser=k,new k}();ve.parser=ve;var zi=ve;it.extend(Li);it.extend(Hi);it.extend(Pi);var Ke={friday:5,saturday:6},lt="",Me="",_e=void 0,Se="",Pt=[],zt=[],Fe=new Map,Ye=[],re=[],Et="",Ue="",Dn=["active","done","crit","milestone","vert"],Ee=[],Rt=!1,Le=!1,Ae="sunday",ie="saturday",Te=0,Ri=d(function(){Ye=[],re=[],Et="",Ee=[],jt=0,xe=void 0,Jt=void 0,tt=[],lt="",Me="",Ue="",_e=void 0,Se="",Pt=[],zt=[],Rt=!1,Le=!1,Te=0,Fe=new Map,qn(),Ae="sunday",ie="saturday"},"clear"),qi=d(function(t){Me=t},"setAxisFormat"),Bi=d(function(){return Me},"getAxisFormat"),Zi=d(function(t){_e=t},"setTickInterval"),Xi=d(function(){return _e},"getTickInterval"),Gi=d(function(t){Se=t},"setTodayMarker"),Qi=d(function(){return Se},"getTodayMarker"),$i=d(function(t){lt=t},"setDateFormat"),ji=d(function(){Rt=!0},"enableInclusiveEndDates"),Ji=d(function(){return Rt},"endDatesAreInclusive"),Ki=d(function(){Le=!0},"enableTopAxis"),ta=d(function(){return Le},"topAxisEnabled"),ea=d(function(t){Ue=t},"setDisplayMode"),na=d(function(){return Ue},"getDisplayMode"),ra=d(function(){return lt},"getDateFormat"),ia=d(function(t){Pt=t.toLowerCase().split(/[\s,]+/)},"setIncludes"),aa=d(function(){return Pt},"getIncludes"),sa=d(function(t){zt=t.toLowerCase().split(/[\s,]+/)},"setExcludes"),oa=d(function(){return zt},"getExcludes"),ca=d(function(){return Fe},"getLinks"),ua=d(function(t){Et=t,Ye.push(t)},"addSection"),la=d(function(){return Ye},"getSections"),fa=d(function(){let t=tn();const e=10;let n=0;for(;!t&&n<e;)t=tn(),n++;return re=tt,re},"getTasks"),Mn=d(function(t,e,n,r){return r.includes(t.format(e.trim()))?!1:n.includes("weekends")&&(t.isoWeekday()===Ke[ie]||t.isoWeekday()===Ke[ie]+1)||n.includes(t.format("dddd").toLowerCase())?!0:n.includes(t.format(e.trim()))},"isInvalidDate"),ha=d(function(t){Ae=t},"setWeekday"),da=d(function(){return Ae},"getWeekday"),ma=d(function(t){ie=t},"setWeekend"),_n=d(function(t,e,n,r){if(!n.length||t.manualEndTime)return;let i;t.startTime instanceof Date?i=it(t.startTime):i=it(t.startTime,e,!0),i=i.add(1,"d");let a;t.endTime instanceof Date?a=it(t.endTime):a=it(t.endTime,e,!0);const[s,p]=ga(i,a,e,n,r);t.endTime=s.toDate(),t.renderEndTime=p},"checkTaskDates"),ga=d(function(t,e,n,r,i){let a=!1,s=null;for(;t<=e;)a||(s=e.toDate()),a=Mn(t,n,r,i),a&&(e=e.add(1,"d")),t=t.add(1,"d");return[e,s]},"fixTaskDates"),be=d(function(t,e,n){n=n.trim();const i=/^after\s+(?<ids>[\d\w- ]+)/.exec(n);if(i!==null){let s=null;for(const M of i.groups.ids.split(" ")){let T=Dt(M);T!==void 0&&(!s||T.endTime>s.endTime)&&(s=T)}if(s)return s.endTime;const p=new Date;return p.setHours(0,0,0,0),p}let a=it(n,e.trim(),!0);if(a.isValid())return a.toDate();{Kt.debug("Invalid date:"+n),Kt.debug("With date format:"+e.trim());const s=new Date(n);if(s===void 0||isNaN(s.getTime())||s.getFullYear()<-1e4||s.getFullYear()>1e4)throw new Error("Invalid date:"+n);return s}},"getStartDate"),Sn=d(function(t){const e=/^(\d+(?:\.\d+)?)([Mdhmswy]|ms)$/.exec(t.trim());return e!==null?[Number.parseFloat(e[1]),e[2]]:[NaN,"ms"]},"parseDuration"),Fn=d(function(t,e,n,r=!1){n=n.trim();const a=/^until\s+(?<ids>[\d\w- ]+)/.exec(n);if(a!==null){let g=null;for(const C of a.groups.ids.split(" ")){let b=Dt(C);b!==void 0&&(!g||b.startTime<g.startTime)&&(g=b)}if(g)return g.startTime;const U=new Date;return U.setHours(0,0,0,0),U}let s=it(n,e.trim(),!0);if(s.isValid())return r&&(s=s.add(1,"d")),s.toDate();let p=it(t);const[M,T]=Sn(n);if(!Number.isNaN(M)){const g=p.add(M,T);g.isValid()&&(p=g)}return p.toDate()},"getEndDate"),jt=0,Ft=d(function(t){return t===void 0?(jt=jt+1,"task"+jt):t},"parseId"),ya=d(function(t,e){let n;e.substr(0,1)===":"?n=e.substr(1,e.length):n=e;const r=n.split(","),i={};Ie(r,i,Dn);for(let s=0;s<r.length;s++)r[s]=r[s].trim();let a="";switch(r.length){case 1:i.id=Ft(),i.startTime=t.endTime,a=r[0];break;case 2:i.id=Ft(),i.startTime=be(void 0,lt,r[0]),a=r[1];break;case 3:i.id=Ft(r[0]),i.startTime=be(void 0,lt,r[1]),a=r[2];break}return a&&(i.endTime=Fn(i.startTime,lt,a,Rt),i.manualEndTime=it(a,"YYYY-MM-DD",!0).isValid(),_n(i,lt,zt,Pt)),i},"compileData"),ka=d(function(t,e){let n;e.substr(0,1)===":"?n=e.substr(1,e.length):n=e;const r=n.split(","),i={};Ie(r,i,Dn);for(let a=0;a<r.length;a++)r[a]=r[a].trim();switch(r.length){case 1:i.id=Ft(),i.startTime={type:"prevTaskEnd",id:t},i.endTime={data:r[0]};break;case 2:i.id=Ft(),i.startTime={type:"getStartDate",startData:r[0]},i.endTime={data:r[1]};break;case 3:i.id=Ft(r[0]),i.startTime={type:"getStartDate",startData:r[1]},i.endTime={data:r[2]};break}return i},"parseData"),xe,Jt,tt=[],Yn={},pa=d(function(t,e){const n={section:Et,type:Et,processed:!1,manualEndTime:!1,renderEndTime:null,raw:{data:e},task:t,classes:[]},r=ka(Jt,e);n.raw.startTime=r.startTime,n.raw.endTime=r.endTime,n.id=r.id,n.prevTaskId=Jt,n.active=r.active,n.done=r.done,n.crit=r.crit,n.milestone=r.milestone,n.vert=r.vert,n.order=Te,Te++;const i=tt.push(n);Jt=n.id,Yn[n.id]=i-1},"addTask"),Dt=d(function(t){const e=Yn[t];return tt[e]},"findTaskById"),va=d(function(t,e){const n={section:Et,type:Et,description:t,task:t,classes:[]},r=ya(xe,e);n.startTime=r.startTime,n.endTime=r.endTime,n.id=r.id,n.active=r.active,n.done=r.done,n.crit=r.crit,n.milestone=r.milestone,n.vert=r.vert,xe=n,re.push(n)},"addTaskOrg"),tn=d(function(){const t=d(function(n){const r=tt[n];let i="";switch(tt[n].raw.startTime.type){case"prevTaskEnd":{const a=Dt(r.prevTaskId);r.startTime=a.endTime;break}case"getStartDate":i=be(void 0,lt,tt[n].raw.startTime.startData),i&&(tt[n].startTime=i);break}return tt[n].startTime&&(tt[n].endTime=Fn(tt[n].startTime,lt,tt[n].raw.endTime.data,Rt),tt[n].endTime&&(tt[n].processed=!0,tt[n].manualEndTime=it(tt[n].raw.endTime.data,"YYYY-MM-DD",!0).isValid(),_n(tt[n],lt,zt,Pt))),tt[n].processed},"compileTask");let e=!0;for(const[n,r]of tt.entries())t(n),e=e&&r.processed;return e},"compileTasks"),Ta=d(function(t,e){let n=e;_t().securityLevel!=="loose"&&(n=Rn.sanitizeUrl(e)),t.split(",").forEach(function(r){Dt(r)!==void 0&&(En(r,()=>{window.open(n,"_self")}),Fe.set(r,n))}),Un(t,"clickable")},"setLink"),Un=d(function(t,e){t.split(",").forEach(function(n){let r=Dt(n);r!==void 0&&r.classes.push(e)})},"setClass"),ba=d(function(t,e,n){if(_t().securityLevel!=="loose"||e===void 0)return;let r=[];if(typeof n=="string"){r=n.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);for(let a=0;a<r.length;a++){let s=r[a].trim();s.startsWith('"')&&s.endsWith('"')&&(s=s.substr(1,s.length-2)),r[a]=s}}r.length===0&&r.push(t),Dt(t)!==void 0&&En(t,()=>{Bn.runFunc(e,...r)})},"setClickFun"),En=d(function(t,e){Ee.push(function(){const n=document.querySelector(`[id="${t}"]`);n!==null&&n.addEventListener("click",function(){e()})},function(){const n=document.querySelector(`[id="${t}-text"]`);n!==null&&n.addEventListener("click",function(){e()})})},"pushFun"),xa=d(function(t,e,n){t.split(",").forEach(function(r){ba(r,e,n)}),Un(t,"clickable")},"setClickEvent"),wa=d(function(t){Ee.forEach(function(e){e(t)})},"bindFunctions"),Ca={getConfig:d(()=>_t().gantt,"getConfig"),clear:Ri,setDateFormat:$i,getDateFormat:ra,enableInclusiveEndDates:ji,endDatesAreInclusive:Ji,enableTopAxis:Ki,topAxisEnabled:ta,setAxisFormat:qi,getAxisFormat:Bi,setTickInterval:Zi,getTickInterval:Xi,setTodayMarker:Gi,getTodayMarker:Qi,setAccTitle:Vn,getAccTitle:Nn,setDiagramTitle:On,getDiagramTitle:Hn,setDisplayMode:ea,getDisplayMode:na,setAccDescription:Wn,getAccDescription:In,addSection:ua,getSections:la,getTasks:fa,addTask:pa,findTaskById:Dt,addTaskOrg:va,setIncludes:ia,getIncludes:aa,setExcludes:sa,getExcludes:oa,setClickEvent:xa,setLink:Ta,getLinks:ca,bindFunctions:wa,parseDuration:Sn,isInvalidDate:Mn,setWeekday:ha,getWeekday:da,setWeekend:ma};function Ie(t,e,n){let r=!0;for(;r;)r=!1,n.forEach(function(i){const a="^\\s*"+i+"\\s*$",s=new RegExp(a);t[0].match(s)&&(e[i]=!0,t.shift(1),r=!0)})}d(Ie,"getTaskTags");var Da=d(function(){Kt.debug("Something is calling, setConf, remove the call")},"setConf"),en={monday:Ot,tuesday:gn,wednesday:yn,thursday:bt,friday:kn,saturday:pn,sunday:Vt},Ma=d((t,e)=>{let n=[...t].map(()=>-1/0),r=[...t].sort((a,s)=>a.startTime-s.startTime||a.order-s.order),i=0;for(const a of r)for(let s=0;s<n.length;s++)if(a.startTime>=n[s]){n[s]=a.endTime,a.order=s+e,s>i&&(i=s);break}return i},"getMaxIntersections"),ht,_a=d(function(t,e,n,r){const i=_t().gantt,a=_t().securityLevel;let s;a==="sandbox"&&(s=Bt("#i"+e));const p=a==="sandbox"?Bt(s.nodes()[0].contentDocument.body):Bt("body"),M=a==="sandbox"?s.nodes()[0].contentDocument:document,T=M.getElementById(e);ht=T.parentElement.offsetWidth,ht===void 0&&(ht=1200),i.useWidth!==void 0&&(ht=i.useWidth);const g=r.db.getTasks();let U=[];for(const x of g)U.push(x.type);U=O(U);const C={};let b=2*i.topPadding;if(r.db.getDisplayMode()==="compact"||i.displayMode==="compact"){const x={};for(const S of g)x[S.section]===void 0?x[S.section]=[S]:x[S.section].push(S);let F=0;for(const S of Object.keys(x)){const _=Ma(x[S],F)+1;F+=_,b+=_*(i.barHeight+i.barGap),C[S]=_}}else{b+=g.length*(i.barHeight+i.barGap);for(const x of U)C[x]=g.filter(F=>F.type===x).length}T.setAttribute("viewBox","0 0 "+ht+" "+b);const X=p.select(`[id="${e}"]`),H=Fi().domain([Jn(g,function(x){return x.startTime}),jn(g,function(x){return x.endTime})]).rangeRound([0,ht-i.leftPadding-i.rightPadding]);function D(x,F){const S=x.startTime,_=F.startTime;let k=0;return S>_?k=1:S<_&&(k=-1),k}d(D,"taskCompare"),g.sort(D),I(g,ht,b),Pn(X,b,ht,i.useMaxWidth),X.append("text").text(r.db.getDiagramTitle()).attr("x",ht/2).attr("y",i.titleTopMargin).attr("class","titleText");function I(x,F,S){const _=i.barHeight,k=_+i.barGap,Y=i.topPadding,l=i.leftPadding,h=Qn().domain([0,U.length]).range(["#00B9FA","#F95002"]).interpolate(dr);W(k,Y,l,F,S,x,r.db.getExcludes(),r.db.getIncludes()),B(l,Y,F,S),V(x,k,Y,l,_,h,F),$(k,Y),w(l,Y,F,S)}d(I,"makeGantt");function V(x,F,S,_,k,Y,l){x.sort((u,o)=>u.vert===o.vert?0:u.vert?1:-1);const y=[...new Set(x.map(u=>u.order))].map(u=>x.find(o=>o.order===u));X.append("g").selectAll("rect").data(y).enter().append("rect").attr("x",0).attr("y",function(u,o){return o=u.order,o*F+S-2}).attr("width",function(){return l-i.rightPadding/2}).attr("height",F).attr("class",function(u){for(const[o,R]of U.entries())if(u.type===R)return"section section"+o%i.numberSectionStyles;return"section section0"}).enter();const m=X.append("g").selectAll("rect").data(x).enter(),E=r.db.getLinks();if(m.append("rect").attr("id",function(u){return u.id}).attr("rx",3).attr("ry",3).attr("x",function(u){return u.milestone?H(u.startTime)+_+.5*(H(u.endTime)-H(u.startTime))-.5*k:H(u.startTime)+_}).attr("y",function(u,o){return o=u.order,u.vert?i.gridLineStartPadding:o*F+S}).attr("width",function(u){return u.milestone?k:u.vert?.08*k:H(u.renderEndTime||u.endTime)-H(u.startTime)}).attr("height",function(u){return u.vert?g.length*(i.barHeight+i.barGap)+i.barHeight*2:k}).attr("transform-origin",function(u,o){return o=u.order,(H(u.startTime)+_+.5*(H(u.endTime)-H(u.startTime))).toString()+"px "+(o*F+S+.5*k).toString()+"px"}).attr("class",function(u){const o="task";let R="";u.classes.length>0&&(R=u.classes.join(" "));let P=0;for(const[K,G]of U.entries())u.type===G&&(P=K%i.numberSectionStyles);let z="";return u.active?u.crit?z+=" activeCrit":z=" active":u.done?u.crit?z=" doneCrit":z=" done":u.crit&&(z+=" crit"),z.length===0&&(z=" task"),u.milestone&&(z=" milestone "+z),u.vert&&(z=" vert "+z),z+=P,z+=" "+R,o+z}),m.append("text").attr("id",function(u){return u.id+"-text"}).text(function(u){return u.task}).attr("font-size",i.fontSize).attr("x",function(u){let o=H(u.startTime),R=H(u.renderEndTime||u.endTime);if(u.milestone&&(o+=.5*(H(u.endTime)-H(u.startTime))-.5*k,R=o+k),u.vert)return H(u.startTime)+_;const P=this.getBBox().width;return P>R-o?R+P+1.5*i.leftPadding>l?o+_-5:R+_+5:(R-o)/2+o+_}).attr("y",function(u,o){return u.vert?i.gridLineStartPadding+g.length*(i.barHeight+i.barGap)+60:(o=u.order,o*F+i.barHeight/2+(i.fontSize/2-2)+S)}).attr("text-height",k).attr("class",function(u){const o=H(u.startTime);let R=H(u.endTime);u.milestone&&(R=o+k);const P=this.getBBox().width;let z="";u.classes.length>0&&(z=u.classes.join(" "));let K=0;for(const[j,at]of U.entries())u.type===at&&(K=j%i.numberSectionStyles);let G="";return u.active&&(u.crit?G="activeCritText"+K:G="activeText"+K),u.done?u.crit?G=G+" doneCritText"+K:G=G+" doneText"+K:u.crit&&(G=G+" critText"+K),u.milestone&&(G+=" milestoneText"),u.vert&&(G+=" vertText"),P>R-o?R+P+1.5*i.leftPadding>l?z+" taskTextOutsideLeft taskTextOutside"+K+" "+G:z+" taskTextOutsideRight taskTextOutside"+K+" "+G+" width-"+P:z+" taskText taskText"+K+" "+G+" width-"+P}),_t().securityLevel==="sandbox"){let u;u=Bt("#i"+e);const o=u.nodes()[0].contentDocument;m.filter(function(R){return E.has(R.id)}).each(function(R){var P=o.querySelector("#"+R.id),z=o.querySelector("#"+R.id+"-text");const K=P.parentNode;var G=o.createElement("a");G.setAttribute("xlink:href",E.get(R.id)),G.setAttribute("target","_top"),K.appendChild(G),G.appendChild(P),G.appendChild(z)})}}d(V,"drawRects");function W(x,F,S,_,k,Y,l,h){if(l.length===0&&h.length===0)return;let y,m;for(const{startTime:P,endTime:z}of Y)(y===void 0||P<y)&&(y=P),(m===void 0||z>m)&&(m=z);if(!y||!m)return;if(it(m).diff(it(y),"year")>5){Kt.warn("The difference between the min and max time is more than 5 years. This will cause performance issues. Skipping drawing exclude days.");return}const E=r.db.getDateFormat(),c=[];let u=null,o=it(y);for(;o.valueOf()<=m;)r.db.isInvalidDate(o,E,l,h)?u?u.end=o:u={start:o,end:o}:u&&(c.push(u),u=null),o=o.add(1,"d");X.append("g").selectAll("rect").data(c).enter().append("rect").attr("id",function(P){return"exclude-"+P.start.format("YYYY-MM-DD")}).attr("x",function(P){return H(P.start)+S}).attr("y",i.gridLineStartPadding).attr("width",function(P){const z=P.end.add(1,"day");return H(z)-H(P.start)}).attr("height",k-F-i.gridLineStartPadding).attr("transform-origin",function(P,z){return(H(P.start)+S+.5*(H(P.end)-H(P.start))).toString()+"px "+(z*x+.5*k).toString()+"px"}).attr("class","exclude-range")}d(W,"drawExcludeDays");function B(x,F,S,_){let k=sr(H).tickSize(-_+F+i.gridLineStartPadding).tickFormat(ne(r.db.getAxisFormat()||i.axisFormat||"%Y-%m-%d"));const l=/^([1-9]\d*)(millisecond|second|minute|hour|day|week|month)$/.exec(r.db.getTickInterval()||i.tickInterval);if(l!==null){const h=l[1],y=l[2],m=r.db.getWeekday()||i.weekday;switch(y){case"millisecond":k.ticks(Yt.every(h));break;case"second":k.ticks(vt.every(h));break;case"minute":k.ticks(Wt.every(h));break;case"hour":k.ticks(Ht.every(h));break;case"day":k.ticks(Tt.every(h));break;case"week":k.ticks(en[m].every(h));break;case"month":k.ticks(Nt.every(h));break}}if(X.append("g").attr("class","grid").attr("transform","translate("+x+", "+(_-50)+")").call(k).selectAll("text").style("text-anchor","middle").attr("fill","#000").attr("stroke","none").attr("font-size",10).attr("dy","1em"),r.db.topAxisEnabled()||i.topAxis){let h=ar(H).tickSize(-_+F+i.gridLineStartPadding).tickFormat(ne(r.db.getAxisFormat()||i.axisFormat||"%Y-%m-%d"));if(l!==null){const y=l[1],m=l[2],E=r.db.getWeekday()||i.weekday;switch(m){case"millisecond":h.ticks(Yt.every(y));break;case"second":h.ticks(vt.every(y));break;case"minute":h.ticks(Wt.every(y));break;case"hour":h.ticks(Ht.every(y));break;case"day":h.ticks(Tt.every(y));break;case"week":h.ticks(en[E].every(y));break;case"month":h.ticks(Nt.every(y));break}}X.append("g").attr("class","grid").attr("transform","translate("+x+", "+F+")").call(h).selectAll("text").style("text-anchor","middle").attr("fill","#000").attr("stroke","none").attr("font-size",10)}}d(B,"makeGrid");function $(x,F){let S=0;const _=Object.keys(C).map(k=>[k,C[k]]);X.append("g").selectAll("text").data(_).enter().append(function(k){const Y=k[0].split(zn.lineBreakRegex),l=-(Y.length-1)/2,h=M.createElementNS("http://www.w3.org/2000/svg","text");h.setAttribute("dy",l+"em");for(const[y,m]of Y.entries()){const E=M.createElementNS("http://www.w3.org/2000/svg","tspan");E.setAttribute("alignment-baseline","central"),E.setAttribute("x","10"),y>0&&E.setAttribute("dy","1em"),E.textContent=m,h.appendChild(E)}return h}).attr("x",10).attr("y",function(k,Y){if(Y>0)for(let l=0;l<Y;l++)return S+=_[Y-1][1],k[1]*x/2+S*x+F;else return k[1]*x/2+F}).attr("font-size",i.sectionFontSize).attr("class",function(k){for(const[Y,l]of U.entries())if(k[0]===l)return"sectionTitle sectionTitle"+Y%i.numberSectionStyles;return"sectionTitle"})}d($,"vertLabels");function w(x,F,S,_){const k=r.db.getTodayMarker();if(k==="off")return;const Y=X.append("g").attr("class","today"),l=new Date,h=Y.append("line");h.attr("x1",H(l)+x).attr("x2",H(l)+x).attr("y1",i.titleTopMargin).attr("y2",_-i.titleTopMargin).attr("class","today"),k!==""&&h.attr("style",k.replace(/,/g,";"))}d(w,"drawToday");function O(x){const F={},S=[];for(let _=0,k=x.length;_<k;++_)Object.prototype.hasOwnProperty.call(F,x[_])||(F[x[_]]=!0,S.push(x[_]));return S}d(O,"checkUnique")},"draw"),Sa={setConf:Da,draw:_a},Fa=d(t=>`
  .mermaid-main-font {
        font-family: ${t.fontFamily};
  }

  .exclude-range {
    fill: ${t.excludeBkgColor};
  }

  .section {
    stroke: none;
    opacity: 0.2;
  }

  .section0 {
    fill: ${t.sectionBkgColor};
  }

  .section2 {
    fill: ${t.sectionBkgColor2};
  }

  .section1,
  .section3 {
    fill: ${t.altSectionBkgColor};
    opacity: 0.2;
  }

  .sectionTitle0 {
    fill: ${t.titleColor};
  }

  .sectionTitle1 {
    fill: ${t.titleColor};
  }

  .sectionTitle2 {
    fill: ${t.titleColor};
  }

  .sectionTitle3 {
    fill: ${t.titleColor};
  }

  .sectionTitle {
    text-anchor: start;
    font-family: ${t.fontFamily};
  }


  /* Grid and axis */

  .grid .tick {
    stroke: ${t.gridColor};
    opacity: 0.8;
    shape-rendering: crispEdges;
  }

  .grid .tick text {
    font-family: ${t.fontFamily};
    fill: ${t.textColor};
  }

  .grid path {
    stroke-width: 0;
  }


  /* Today line */

  .today {
    fill: none;
    stroke: ${t.todayLineColor};
    stroke-width: 2px;
  }


  /* Task styling */

  /* Default task */

  .task {
    stroke-width: 2;
  }

  .taskText {
    text-anchor: middle;
    font-family: ${t.fontFamily};
  }

  .taskTextOutsideRight {
    fill: ${t.taskTextDarkColor};
    text-anchor: start;
    font-family: ${t.fontFamily};
  }

  .taskTextOutsideLeft {
    fill: ${t.taskTextDarkColor};
    text-anchor: end;
  }


  /* Special case clickable */

  .task.clickable {
    cursor: pointer;
  }

  .taskText.clickable {
    cursor: pointer;
    fill: ${t.taskTextClickableColor} !important;
    font-weight: bold;
  }

  .taskTextOutsideLeft.clickable {
    cursor: pointer;
    fill: ${t.taskTextClickableColor} !important;
    font-weight: bold;
  }

  .taskTextOutsideRight.clickable {
    cursor: pointer;
    fill: ${t.taskTextClickableColor} !important;
    font-weight: bold;
  }


  /* Specific task settings for the sections*/

  .taskText0,
  .taskText1,
  .taskText2,
  .taskText3 {
    fill: ${t.taskTextColor};
  }

  .task0,
  .task1,
  .task2,
  .task3 {
    fill: ${t.taskBkgColor};
    stroke: ${t.taskBorderColor};
  }

  .taskTextOutside0,
  .taskTextOutside2
  {
    fill: ${t.taskTextOutsideColor};
  }

  .taskTextOutside1,
  .taskTextOutside3 {
    fill: ${t.taskTextOutsideColor};
  }


  /* Active task */

  .active0,
  .active1,
  .active2,
  .active3 {
    fill: ${t.activeTaskBkgColor};
    stroke: ${t.activeTaskBorderColor};
  }

  .activeText0,
  .activeText1,
  .activeText2,
  .activeText3 {
    fill: ${t.taskTextDarkColor} !important;
  }


  /* Completed task */

  .done0,
  .done1,
  .done2,
  .done3 {
    stroke: ${t.doneTaskBorderColor};
    fill: ${t.doneTaskBkgColor};
    stroke-width: 2;
  }

  .doneText0,
  .doneText1,
  .doneText2,
  .doneText3 {
    fill: ${t.taskTextDarkColor} !important;
  }


  /* Tasks on the critical line */

  .crit0,
  .crit1,
  .crit2,
  .crit3 {
    stroke: ${t.critBorderColor};
    fill: ${t.critBkgColor};
    stroke-width: 2;
  }

  .activeCrit0,
  .activeCrit1,
  .activeCrit2,
  .activeCrit3 {
    stroke: ${t.critBorderColor};
    fill: ${t.activeTaskBkgColor};
    stroke-width: 2;
  }

  .doneCrit0,
  .doneCrit1,
  .doneCrit2,
  .doneCrit3 {
    stroke: ${t.critBorderColor};
    fill: ${t.doneTaskBkgColor};
    stroke-width: 2;
    cursor: pointer;
    shape-rendering: crispEdges;
  }

  .milestone {
    transform: rotate(45deg) scale(0.8,0.8);
  }

  .milestoneText {
    font-style: italic;
  }
  .doneCritText0,
  .doneCritText1,
  .doneCritText2,
  .doneCritText3 {
    fill: ${t.taskTextDarkColor} !important;
  }

  .vert {
    stroke: ${t.vertLineColor};
  }

  .vertText {
    font-size: 15px;
    text-anchor: middle;
    fill: ${t.vertLineColor} !important;
  }

  .activeCritText0,
  .activeCritText1,
  .activeCritText2,
  .activeCritText3 {
    fill: ${t.taskTextDarkColor} !important;
  }

  .titleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${t.titleColor||t.textColor};
    font-family: ${t.fontFamily};
  }
`,"getStyles"),Ya=Fa,Wa={parser:zi,db:Ca,renderer:Sa,styles:Ya};export{Wa as diagram};
