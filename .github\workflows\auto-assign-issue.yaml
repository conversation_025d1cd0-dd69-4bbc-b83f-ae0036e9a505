name: Issue assignment

on:
  issues:
    types: [opened]

jobs:
  auto-assign:
    runs-on: ubuntu-latest
    permissions:
      issues: write
    steps:
      - name: "Auto-assign issue"
        uses: pozil/auto-assign-issue@v2
        with:
          repo-token: ${{ secrets.CI_GITHUB_TOKEN }}
          assignees: <PERSON><PERSON><PERSON><PERSON>,<PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>
          numOfAssignee: 1
      # - name: "Add default labels"
      #   uses: actions-ecosystem/action-add-labels@v1
      #   with:
      #     github_token: ${{ secrets.GITHUB_TOKEN }}
      #     labels: |
      #       "needs-triage"
