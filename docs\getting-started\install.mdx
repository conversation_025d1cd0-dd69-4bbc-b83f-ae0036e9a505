---
title: "Install"
description: "Get Continue installed in your favorite IDE in just a few steps."
---

<Tabs>
<Tab title="VS Code">

<Steps>
<Step title="Install from Marketplace">
Click `Install` on the [Continue extension page in the Visual Studio Marketplace](https://marketplace.visualstudio.com/items?itemName=Continue.continue)
</Step>

<Step title="Install in VS Code">
  This will open the Continue extension page in VS Code, where you will need to
  click `Install` again
</Step>

<Step title="Move to Right Sidebar">
The Continue logo will appear on the left sidebar. For a better experience, move Continue to the right sidebar

![move-to-right-sidebar](/images/move-to-right-sidebar-b2d315296198e41046fc174d8178f30a.gif)

</Step>

<Step title="Sign In">
[Sign in to the hub](https://auth.continue.dev/) to get started with your first assistant
</Step>
</Steps>

<Info>
  If you have any problems, see the [troubleshooting guide](/troubleshooting) or
  ask for help in [our Discord](https://discord.gg/NWtdYexhMs)
</Info>

</Tab>

<Tab title="JetBrains">

<Steps>
<Step title="Open Settings">
Open your JetBrains IDE and open **Settings** using `Ctrl` + `Alt` + `S`
</Step>

<Step title="Find Continue Plugin">
  Select **Plugins** on the sidebar and search for "Continue" in the marketplace
</Step>

<Step title="Install Plugin">
Click `Install`, which will cause the Continue logo to show up on the right toolbar

![jetbrains-getting-started.png](/images/getting-started/images/jetbrains-getting-started-d62b7edee1cdd58508c5075faf285955.png)

</Step>

<Step title="Sign In">
[Sign in to the hub](https://auth.continue.dev/) to get started with your first assistant
</Step>
</Steps>

<Info>
  If you have any problems, see the [troubleshooting guide](/troubleshooting) or
  ask for help in [our Discord](https://discord.com/invite/EfJEfdFnDQ)
</Info>

</Tab>
</Tabs>

## Signing in

Click "Get started" to sign in to the hub and start using assistants.

![Hub Onboarding in the Extension](/images/getting-started/images/hub-onboarding-card-81abd457b6d131c4b0aa89a5a6d647d3.png)
