// @ts-nocheck

function* getAddress(person: Person): Address {
  // TODO
}

function* getFirstAddress(people: Person[]): Address {
  // TODO
}

function* logPerson(person: Person) {
  // TODO
}

function* getHardcodedAddress(): Address {
  // TODO
}

function* getAddresses(people: Person[]): Address[] {
  // TODO
}

function* logPersonWithAddress(person: Person<Address>): Person<Address> {
  // TODO
}

function* logPersonOrAddress(person: Person | Address): Person | Address {
  // TODO
}

function* logPersonAndAddress(person: Person, address: Address) {
  // TODO
}
