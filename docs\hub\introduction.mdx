---
title: "Introduction"
---

[Continue Hub](https://hub.continue.dev) makes it simple to create custom AI code [assistants](/hub/assistants/intro), providing a registry for defining, managing, and sharing assistant [building blocks](/hub/blocks/intro).

Assistants can contain several types of blocks, including [models](/hub/blocks/block-types#models), [rules](/hub/blocks/block-types#rules), [context providers](/hub/blocks/block-types#context), [prompts](/hub/blocks/block-types#prompts), [docs](/hub/blocks/block-types#docs), [data destinations](/hub/blocks/block-types#data), and [MCP servers](/hub/blocks/block-types#mcp-servers).

Continue Hub also makes it easy for engineering leaders to centrally [configure](/hub/secrets/secret-types) and [govern](/hub/governance/org-permissions) blocks and assistants for their organization.
