name: Release @continuedev/fetch

on:
  push:
    branches:
      - main
    paths:
      - "packages/fetch/**"

jobs:
  release:
    uses: ./.github/workflows/reusable-release.yml
    with:
      package-name: "fetch"
      package-path: "packages/fetch"
    secrets:
      SEMANTIC_RELEASE_GITHUB_TOKEN: ${{ secrets.SEMANTIC_RELEASE_GITHUB_TOKEN }}
      SEMANTIC_RELEASE_NPM_TOKEN: ${{ secrets.SEMANTIC_RELEASE_NPM_TOKEN }}
