name: Release @continuedev/llm-info

on:
  push:
    branches:
      - main
    paths:
      - "packages/llm-info/**"

jobs:
  release:
    uses: ./.github/workflows/reusable-release.yml
    with:
      package-name: "llm-info"
      package-path: "packages/llm-info"
    secrets:
      SEMANTIC_RELEASE_GITHUB_TOKEN: ${{ secrets.SEMANTIC_RELEASE_GITHUB_TOKEN }}
      SEMANTIC_RELEASE_NPM_TOKEN: ${{ secrets.SEMANTIC_RELEASE_NPM_TOKEN }}
