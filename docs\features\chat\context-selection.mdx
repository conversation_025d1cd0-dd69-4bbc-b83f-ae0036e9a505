## Input

Typing a question or instructions into the input box is the only required context.

## Highlighted Code

The highlighted code you've selected by pressing cmd/ctrl + L (VS Code) or cmd/ctrl + J (JetBrains) will be included in your prompt.

## Active File

You can include the currently open file as context by pressing opt/alt + enter when you send your request.

## Specific File

You can include a specific file in your current workspace by typing '@Files' and selecting the file.

## Specific Folder

You can include a folder in your current workspace by typing '@Folder' and selecting the directory.

## Codebase Search

You can automatically include relevant files from your codebase by typing '@Codebase'.

## Documentation Site

You can include a documentation site as context by typing '@Docs' and selecting the site.

## Terminal Contents

You can include the contents of the terminal in your IDE by typing '@Terminal'.

## Git Diff

You can include all of the changes you've made to your current branch by typing '@Git Diff'.

## Other Context

You can see a full list of built-in context providers [here](/customize/custom-providers) and learn about how to create your own context provider [here](/guides/build-your-own-context-provider).
