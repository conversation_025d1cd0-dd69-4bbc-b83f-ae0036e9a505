name: Reusable Release Workflow

on:
  workflow_call:
    inputs:
      package-name:
        required: true
        type: string
      package-path:
        required: true
        type: string
    secrets:
      SEMANTIC_RELEASE_GITHUB_TOKEN:
        required: true
      SEMANTIC_RELEASE_NPM_TOKEN:
        required: true

permissions:
  contents: write
  issues: write
  pull-requests: write

jobs:
  release:
    name: Release ${{ inputs.package-name }}
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ${{ inputs.package-path }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Use Node.js from .nvmrc
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - name: Install dependencies
        run: npm ci

      - name: Build
        run: npm run build

      - name: Run tests
        run: npm test

      - name: Release
        env:
          GITHUB_TOKEN: ${{ secrets.SEMANTIC_RELEASE_GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.SEMANTIC_RELEASE_NPM_TOKEN }}
          NODE_AUTH_TOKEN: ${{ secrets.SEMANTIC_RELEASE_NPM_TOKEN }}
        run: npx semantic-release