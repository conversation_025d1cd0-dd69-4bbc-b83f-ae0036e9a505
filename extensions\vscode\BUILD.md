# Continue VSCode Extension 构建指南

本文档详细说明了如何从源代码构建 Continue VSCode 扩展插件。

## 前置要求

- Node.js >= 20.19.0 (建议使用 .nvmrc 中指定的版本)
- npm 或 yarn
- Git

## 完整构建流程

### 第一步：根目录依赖安装

```bash
cd c:\code\continuedev-continue
npm install
```

### 第二步：构建 packages 下的内部包

**重要：必须按以下依赖顺序构建**

```bash
# 1. config-types (基础类型定义)
cd packages\config-types
npm install
npm run build

# 2. config-yaml (配置解析)
cd ..\config-yaml
npm install
npm run build

# 3. fetch (网络请求工具)
cd ..\fetch
npm install
npm run build

# 4. openai-adapters (OpenAI适配器)
cd ..\openai-adapters
npm install
npm run build

# 5. continue-sdk (SDK)
cd ..\continue-sdk
npm install
npm run build

# 6. llm-info (LLM信息)
cd ..\llm-info
npm install
npm run build
```

### 第三步：安装 core 目录依赖

```bash
cd ..\..\core
npm install
```

*注意：core 目录不需要单独构建，它会在后续步骤中被处理*

### 第四步：构建 GUI

```bash
cd ..\gui
npm install
npm run build
```

### 第五步：构建 VSCode 插件

```bash
cd ..\extensions\vscode
npm install

# 创建必要的目录（如果不存在）
mkdir build

# 运行预打包脚本（准备所有资源）
npm run prepackage

# 编译和压缩代码
npm run vscode:prepublish

# 生成 .vsix 文件
npm run package
```

## 构建命令说明

### 开发环境构建

```bash
# TypeScript 类型检查
npm run tsc:check

# esbuild 编译（带源码映射）
npm run esbuild

# esbuild 监听模式（自动重新编译）
npm run esbuild-watch

# 启动 GUI 开发服务器
cd ../../gui && npm run dev
```

### 生产环境构建

```bash
# 生产环境编译（压缩）
npm run vscode:prepublish

# 完整预打包流程
npm run prepackage

# 生成标准版本 .vsix 文件
npm run package

# 生成预发布版本 .vsix 文件
npm run package:pre-release
```

### 跨平台打包

```bash
# 为所有支持的平台打包
npm run package-all

# 为特定平台打包
npm run package -- --target win32-x64
npm run package -- --target darwin-arm64
npm run package -- --target linux-x64
npm run package -- --target linux-arm64
```

**支持的平台：**
- `win32-x64`
- `linux-x64`
- `linux-arm64`
- `darwin-x64`
- `darwin-arm64`

## 输出文件

构建完成后，`.vsix` 文件会生成在：
```
extensions/vscode/build/continue-{version}.vsix
```

## 版本管理

### 版本号位置
主要版本号在 `extensions/vscode/package.json` 中：
```json
{
  "name": "continue",
  "version": "1.1.65",
  "publisher": "Continue"
}
```

### 版本号格式
- **主版本格式**：`1.0.x` （稳定版本）
- **预览版格式**：`1.1.x` （预览版本）
- **版本规则**：奇数次版本号(1.1.x)用于预览版，偶数次版本号(1.0.x)用于正式版

### 手动更新版本
```bash
cd extensions/vscode
npm version patch --no-git-tag-version
```

## 常见问题

### 1. 构建目录不存在错误
```
Error: ENOENT: no such file or directory, open './build/meta.json'
```
**解决方案：**
```bash
mkdir build
```

### 2. 依赖包找不到错误
```
Cannot find module '@continuedev/fetch'
```
**解决方案：** 确保按照第二步的顺序构建所有内部包

### 3. GUI 构建失败
**解决方案：** 确保先构建了所有 packages 中的依赖包

### 4. 网络问题
如果遇到网络超时，可以尝试：
```bash
npm config set registry https://registry.npmmirror.com/
```

## 测试安装

构建完成后，可以通过以下方式测试：

1. **VSCode 命令行安装：**
   ```bash
   code --install-extension extensions/vscode/build/continue-{version}.vsix
   ```

2. **VSCode 界面安装：**
   - 打开 VSCode
   - 按 `Ctrl+Shift+P` 打开命令面板
   - 输入 "Extensions: Install from VSIX..."
   - 选择生成的 .vsix 文件

## 发布流程

项目使用自动化发布流程：
- **预览版**：推送到特定分支时自动发布
- **正式版**：创建 `v1.0.x-vscode` 格式的 GitHub Release 时触发
- **发布渠道**：VS Code Marketplace 和 Open VSX Registry

## 开发调试

使用 VSCode 的任务系统进行开发：
```bash
# 在项目根目录
code .
# 按 Ctrl+Shift+P，运行 "Tasks: Run Task"
# 选择 "vscode-extension:build"
```

这将启动完整的开发环境，包括类型检查、代码编译和 GUI 开发服务器。

## 快速构建脚本

为了简化构建过程，可以创建一个批处理脚本来自动执行所有步骤。
