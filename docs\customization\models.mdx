---
title: "Model Blocks"
description: "These blocks form the foundation of the entire assistant experience, offering different specialized capabilities:"
---

- **[Cha<PERSON>](/customize/model-roles/chat)**: Power conversational interactions about code and provide detailed guidance
- **[Edit](/customize/model-roles/edit)**: Handle complex code transformations and refactoring tasks
- **[Apply](/customize/model-roles/apply)**: Execute targeted code modifications with high accuracy
- **[Autocomplete](/customize/model-roles/autocomplete)**: Provide real-time suggestions as developers type
- **[Embedding](/customize/model-roles/embeddings)**: Transform code into vector representations for semantic search
- **[Reranker](/customize/model-roles/reranking)**: Improve search relevance by ordering results based on semantic meaning

![Model Blocks Overview](/images/customization/images/model-blocks-overview-36c30e7e01928d7a9b5b26ff1639c34b.png)

## Learn More

Continue supports [many model providers](/customization/models#openai), including Anthropic, OpenAI, Gemini, Ollama, Amazon Bedrock, Azure, xAI, DeepSeek, and more. Models can have various roles like `chat`, `edit`, `apply`, `autocomplete`, `embed`, and `rerank`.

Read more about roles [here](/customize/model-roles) and view [`models`](/reference#models) in the YAML Reference.
