---
title: "Edit an Assistant"
description: "New versions of an assistant can be created and published using the sidebar."
---

![Remix Assistant <PERSON><PERSON>](/images/hub/assistants/images/assistant-create-sidebar-608be49973f2a7723212adeb52dbcafb.png)

First, select an assistant from the dropdown at the top.

While editing an assistant, you can explore the hub and click "Add Block" from a block page to add it to your assistant.

For blocks that require secret values like API keys, you will see a small notification on the block's tile in the sidebar that will indicate if action is needed.

To delete a block, click the trash icon.

If a block you want to use does not exist yet, you can [create a new block](/hub/blocks/create-a-block).

When you are done editing, click "Publish" to publish a new version of the assistant.

Click "Open VS Code" or "Open JetBrains" to open your IDE for using the assistant.
