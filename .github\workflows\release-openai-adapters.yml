name: Release @continuedev/openai-adapters

on:
  push:
    branches:
      - main
    paths:
      - "packages/openai-adapters/**"

jobs:
  release:
    uses: ./.github/workflows/reusable-release.yml
    with:
      package-name: "openai-adapters"
      package-path: "packages/openai-adapters"
    secrets:
      SEMANTIC_RELEASE_GITHUB_TOKEN: ${{ secrets.SEMANTIC_RELEASE_GITHUB_TOKEN }}
      SEMANTIC_RELEASE_NPM_TOKEN: ${{ secrets.SEMANTIC_RELEASE_NPM_TOKEN }}
