name: Release @continuedev/config-yaml

on:
  push:
    branches:
      - main
    paths:
      - "packages/config-yaml/**"

jobs:
  release:
    uses: ./.github/workflows/reusable-release.yml
    with:
      package-name: "config-yaml"
      package-path: "packages/config-yaml"
    secrets:
      SEMANTIC_RELEASE_GITHUB_TOKEN: ${{ secrets.SEMANTIC_RELEASE_GITHUB_TOKEN }}
      SEMANTIC_RELEASE_NPM_TOKEN: ${{ secrets.SEMANTIC_RELEASE_NPM_TOKEN }}
