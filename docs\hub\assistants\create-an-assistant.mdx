---
title: "Create an assistant"
---

## Remix an assistant

You should remix an assistant if you want to use it after some modifications.

By clicking the “remix” button, you’ll be taken to the “Create a remix” page.

![Remix Assistant Button](/images/hub/assistants/images/assistant-remix-button-728ececa73983140eeab42e33cc3903b.png)

Once here, you’ll be able to

1. add or remove blocks in YAML configuration
2. change the name, description, icon, etc.

Clicking “Create assistant” will make this assistant available for use.

## Create an assistant from scratch

To create an assistant from scratch, select “New assistant” in the top bar.

![New assistant button](/images/hub/assistants/images/assistant-new-button-16070e2cff9ce25ec9ebfeecb0e2f2b5.png)

Choose a name, slug, description, and icon for your assistant.

The easiest way to create an assistant is to click "Create assistant" with the default configuration and then add / remove blocks using the sidebar.

Alternatively, you can edit the assistant YAML directly before clicking "Create assistant". Refer to examples of assistants on [hub.continue.dev](https://hub.continue.dev/explore/assistants) and visit the [YAML Reference](/reference#complete-yaml-config-example) docs for more details.

![New assistant YAML](/images/hub/assistants/images/assistant-create-yaml-a991e5a81506f1c3ba611a664a50734c.png)
