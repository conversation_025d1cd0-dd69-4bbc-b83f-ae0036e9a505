---
title: "Overview"
---

## Model & Setup Guides

- [Using Ollama with Continue](/guides/ollama-guide) - Local AI development with <PERSON>llama
- [Setting up Codestral](/guides/set-up-codestral) - Configure Mi<PERSON><PERSON>'s Codestral model
- [How to Self-Host a Model](/guides/how-to-self-host-a-model) - Self-hosting AI models
- [Running Continue Without Internet](/guides/running-continue-without-internet) - Offline development setup
- [Llama 3.1 Setup](/guides/llama3.1) - Getting started with Llama 3.1

## Advanced Tutorials

- [Build Your Own Context Provider](/guides/build-your-own-context-provider) - Create custom context providers
- [Custom Code RAG](/guides/custom-code-rag) - Implement custom retrieval-augmented generation

## Contributing

Have a guide idea or found an issue? We welcome contributions! Check our [GitHub repository](https://github.com/continuedev/continue) to get involved.
