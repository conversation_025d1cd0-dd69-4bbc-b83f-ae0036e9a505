---
title: "Introduction"
---

**Continue enables developers to create, share, and use custom AI code assistants with our open-source [VS Code](https://marketplace.visualstudio.com/items?itemName=Continue.continue) and [JetBrains](https://plugins.jetbrains.com/plugin/22707-continue-extension) extensions and [hub of models, rules, prompts, docs, and other building blocks](https://hub.continue.dev)**

- [Chat](/features/chat/quick-start) to understand and iterate on code in the sidebar
- [Autocomplete](/features/autocomplete/quick-start) to receive inline code suggestions as you type
- [Edit](/features/edit/quick-start) to modify code without leaving your current file
- [Agent](/features/agent/quick-start) to make more substantial changes to your codebase
