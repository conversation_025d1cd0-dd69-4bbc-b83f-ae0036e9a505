name: "CLA Assistant"
on:
  issue_comment:
    types: [created]
  pull_request_target:
    types: [opened, closed, synchronize]

permissions:
  actions: write
  contents: write
  pull-requests: write
  statuses: write

jobs:
  CLAAssistant:
    runs-on: ubuntu-latest
    # Only run this workflow on the main repository (continuedev/continue)
    if: github.repository == 'continuedev/continue'
    steps:
      - name: "CLA Assistant"
        if: (contains(github.event.comment.body, 'recheck') || contains(github.event.comment.body, 'I have read the CLA Document and I hereby sign the CLA')) || github.event_name == 'pull_request_target'
        uses: contributor-assistant/github-action@v2.6.1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          path-to-signatures: "signatures/version1/cla.json"
          path-to-document: "https://github.com/continuedev/continue/blob/main/CLA.md"
          branch: cla-signatures
          # Bots and CLAs signed outside of GitHub
          allowlist: dependabot[bot],fbricon,pan<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,ow<PERSON><PERSON><PERSON>,halfline
          signed-commit-message: "CLA signed in $pullRequestNo"
