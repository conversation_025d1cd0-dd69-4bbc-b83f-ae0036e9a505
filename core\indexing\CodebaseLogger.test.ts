import fs from "fs";
import path from "path";
import { CodebaseLogger } from "./CodebaseLogger";
import { getCodebaseIndexingLogsPath, getCodebaseRetrievalLogsPath } from "../util/paths";

describe("CodebaseLogger", () => {
  let logger: CodebaseLogger;
  
  beforeEach(() => {
    // Reset singleton instance for each test
    (CodebaseLogger as any).instance = null;
    logger = CodebaseLogger.getInstance({
      enabled: true,
      logLevel: "debug",
      maxLogFileSize: 1024 * 1024, // 1MB for testing
      logRetentionDays: 1,
      bufferSize: 5, // Small buffer for testing
    });
  });

  afterEach(async () => {
    // Clean up log files after each test
    await logger.flush();
    try {
      const indexingLogPath = getCodebaseIndexingLogsPath();
      const retrievalLogPath = getCodebaseRetrievalLogsPath();
      if (fs.existsSync(indexingLogPath)) {
        fs.unlinkSync(indexingLogPath);
      }
      if (fs.existsSync(retrievalLogPath)) {
        fs.unlinkSync(retrievalLogPath);
      }
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  describe("Indexing Logs", () => {
    it("should log indexing start", async () => {
      logger.logIndexingStart({
        totalFiles: 10,
        indexType: "chunks",
        branch: "main",
      });

      await logger.flush();

      const logPath = getCodebaseIndexingLogsPath();
      expect(fs.existsSync(logPath)).toBe(true);

      const logContent = fs.readFileSync(logPath, "utf8");
      const logEntry = JSON.parse(logContent.trim());

      expect(logEntry.type).toBe("indexing_start");
      expect(logEntry.data.totalFiles).toBe(10);
      expect(logEntry.data.indexType).toBe("chunks");
      expect(logEntry.data.branch).toBe("main");
    });

    it("should log file processing", async () => {
      logger.logFileProcessing({
        filepath: "/test/file.ts",
        fileSize: 1024,
        language: "typescript",
        processingTimeMs: 50,
        chunksGenerated: 3,
      });

      await logger.flush();

      const logPath = getCodebaseIndexingLogsPath();
      const logContent = fs.readFileSync(logPath, "utf8");
      const logEntry = JSON.parse(logContent.trim());

      expect(logEntry.type).toBe("file_processing");
      expect(logEntry.data.filepath).toBe("/test/file.ts");
      expect(logEntry.data.chunksGenerated).toBe(3);
    });

    it("should log chunk extraction", async () => {
      logger.logChunkExtraction({
        filepath: "/test/file.ts",
        totalChunks: 2,
        chunks: [
          {
            index: 0,
            startLine: 1,
            endLine: 10,
            contentLength: 200,
            digest: "abc123",
          },
          {
            index: 1,
            startLine: 11,
            endLine: 20,
            contentLength: 150,
            digest: "def456",
          },
        ],
      });

      await logger.flush();

      const logPath = getCodebaseIndexingLogsPath();
      const logContent = fs.readFileSync(logPath, "utf8");
      const logEntry = JSON.parse(logContent.trim());

      expect(logEntry.type).toBe("chunk_extraction");
      expect(logEntry.data.totalChunks).toBe(2);
      expect(logEntry.data.chunks).toHaveLength(2);
    });
  });

  describe("Retrieval Logs", () => {
    it("should log retrieval start", async () => {
      logger.logRetrievalStart({
        originalQuery: "test query",
        fullInput: "test query with context",
        nRetrieve: 20,
        nFinal: 10,
        useReranking: true,
        filterDirectory: "/test",
      });

      await logger.flush();

      const logPath = getCodebaseRetrievalLogsPath();
      expect(fs.existsSync(logPath)).toBe(true);

      const logContent = fs.readFileSync(logPath, "utf8");
      const logEntry = JSON.parse(logContent.trim());

      expect(logEntry.type).toBe("retrieval_start");
      expect(logEntry.data.originalQuery).toBe("test query");
      expect(logEntry.data.useReranking).toBe(true);
    });

    it("should log vector search", async () => {
      logger.logVectorSearch({
        query: "test query",
        embeddingModel: "text-embedding-ada-002",
        searchTimeMs: 100,
        resultsCount: 5,
        results: [
          {
            filepath: "/test/file1.ts",
            chunkIndex: 0,
            score: 0.95,
            startLine: 1,
            endLine: 10,
            contentPreview: "function test() { return 'hello'; }",
          },
        ],
      });

      await logger.flush();

      const logPath = getCodebaseRetrievalLogsPath();
      const logContent = fs.readFileSync(logPath, "utf8");
      const logEntry = JSON.parse(logContent.trim());

      expect(logEntry.type).toBe("vector_search");
      expect(logEntry.data.embeddingModel).toBe("text-embedding-ada-002");
      expect(logEntry.data.results).toHaveLength(1);
    });

    it("should log reranking", async () => {
      logger.logReranking({
        query: "test query",
        rerankModel: "rerank-model",
        inputChunksCount: 10,
        outputChunksCount: 5,
        processingTimeMs: 200,
        scores: [0.9, 0.8, 0.7, 0.6, 0.5],
        rerankedResults: [
          {
            filepath: "/test/file1.ts",
            chunkIndex: 0,
            originalScore: 0.8,
            rerankScore: 0.9,
            startLine: 1,
            endLine: 10,
          },
        ],
      });

      await logger.flush();

      const logPath = getCodebaseRetrievalLogsPath();
      const logContent = fs.readFileSync(logPath, "utf8");
      const logEntry = JSON.parse(logContent.trim());

      expect(logEntry.type).toBe("reranking");
      expect(logEntry.data.rerankModel).toBe("rerank-model");
      expect(logEntry.data.scores).toHaveLength(5);
    });
  });

  describe("Configuration", () => {
    it("should respect log level filtering", async () => {
      const debugLogger = CodebaseLogger.getInstance({
        enabled: true,
        logLevel: "warn",
      });

      debugLogger.logFileProcessing({
        filepath: "/test/file.ts",
        fileSize: 1024,
        processingTimeMs: 50,
        chunksGenerated: 3,
      }, "debug"); // This should be filtered out

      debugLogger.logFileProcessing({
        filepath: "/test/file2.ts",
        fileSize: 2048,
        processingTimeMs: 100,
        chunksGenerated: 5,
        error: "Test error",
      }, "error"); // This should be logged

      await debugLogger.flush();

      const logPath = getCodebaseIndexingLogsPath();
      if (fs.existsSync(logPath)) {
        const logContent = fs.readFileSync(logPath, "utf8");
        const lines = logContent.trim().split('\n').filter(line => line.length > 0);
        
        // Should only have the error log, not the debug log
        expect(lines).toHaveLength(1);
        const logEntry = JSON.parse(lines[0]);
        expect(logEntry.level).toBe("error");
        expect(logEntry.data.error).toBe("Test error");
      }
    });

    it("should not log when disabled", async () => {
      const disabledLogger = CodebaseLogger.getInstance({
        enabled: false,
      });

      disabledLogger.logIndexingStart({
        totalFiles: 5,
        indexType: "chunks",
      });

      await disabledLogger.flush();

      const logPath = getCodebaseIndexingLogsPath();
      expect(fs.existsSync(logPath)).toBe(false);
    });
  });

  describe("Session Management", () => {
    it("should generate unique session IDs", () => {
      const sessionId1 = logger.getSessionId();
      logger.newSession();
      const sessionId2 = logger.getSessionId();

      expect(sessionId1).not.toBe(sessionId2);
      expect(sessionId1).toMatch(/^\d+-[a-z0-9]{9}$/);
      expect(sessionId2).toMatch(/^\d+-[a-z0-9]{9}$/);
    });
  });
});
