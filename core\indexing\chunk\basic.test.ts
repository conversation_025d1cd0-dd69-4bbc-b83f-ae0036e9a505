// Generated by continue
// core/indexing/chunk/basic.test.ts

import { basicChunker } from "./basic.js";
import { countTokensAsync } from "../../llm/countTokens";
import { ChunkWithoutID } from "../../index";

// jest.mock("../../llm/countTokens", () => ({
//   countTokensAsync: jest.fn(),
// }));

describe.skip("basicChunker", () => {
  beforeEach(() => {
    jest.resetAllMocks();
  });

  it("should yield no chunks for empty content", async () => {
    const contents = "";
    const maxChunkSize = 10;
    const chunks: ChunkWithoutID[] = [];

    for await (const chunk of basicChunker(contents, maxChunkSize)) {
      chunks.push(chunk);
    }

    expect(chunks).toHaveLength(0);
  });

  it("should yield a single chunk if whole content fits within the max size", async () => {
    (countTokensAsync as jest.Mock).mockResolvedValue(1);
    const contents = "line1\nline2\nline3";
    const maxChunkSize = 10;
    const expectedChunks: ChunkWithoutID[] = [
      { content: "line1\nline2\nline3\n", startLine: 0, endLine: 2 },
    ];

    const chunks: ChunkWithoutID[] = [];
    for await (const chunk of basicChunker(contents, maxChunkSize)) {
      chunks.push(chunk);
    }

    expect(chunks).toEqual(expectedChunks);
  });

  it("should yield multiple chunks when content exceeds the max size", async () => {
    (countTokensAsync as jest.Mock).mockImplementation(async (line: string) => {
      if (line === "line3") return 5;
      return 3; // Mock returning 3 tokens per line except for "line3"
    });

    const contents = "line1\nline2\nline3\nline4\nline5";
    const maxChunkSize = 8;
    const expectedChunks: ChunkWithoutID[] = [
      { content: "line1\nline2\n", startLine: 0, endLine: 1 },
      { content: "line3\n", startLine: 2, endLine: 2 },
      { content: "line4\nline5\n", startLine: 3, endLine: 4 },
    ];

    const chunks: ChunkWithoutID[] = [];
    for await (const chunk of basicChunker(contents, maxChunkSize)) {
      chunks.push(chunk);
    }

    expect(chunks).toEqual(expectedChunks);
  });

  it("should skip lines that exceed the max chunk size", async () => {
    (countTokensAsync as jest.Mock).mockImplementation(async (line: string) => {
      if (line === "line3") return 15; // Making it explicitly exceed max size
      return 3;
    });

    const contents = "line1\nline2\nline3\nline4\nline5";
    const maxChunkSize = 8;
    const expectedChunks: ChunkWithoutID[] = [
      { content: "line1\nline2\n", startLine: 0, endLine: 1 },
      { content: "line4\nline5\n", startLine: 3, endLine: 4 },
    ];

    const chunks: ChunkWithoutID[] = [];
    for await (const chunk of basicChunker(contents, maxChunkSize)) {
      chunks.push(chunk);
    }

    expect(chunks).toEqual(expectedChunks);
  });
});
