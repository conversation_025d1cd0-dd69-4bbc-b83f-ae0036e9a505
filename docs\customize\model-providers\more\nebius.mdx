---
title: "Nebius"
---

You can get an API key from the [Nebius AI Studio API keys page](https://studio.nebius.ai/settings/api-keys)

## Availible models

Available models can be found on the [Nebius AI Studio models page](https://studio.nebius.ai/models/text2text)

## Chat model

<Tabs>
  <Tab title="YAML">
  ```yaml title="config.yaml"
  models:
    - name: DeepSeek R1
      provider: nebius
      model: deepseek-ai/DeepSeek-R1
      apiKey: <YOUR_NEBIUS_API_KEY>
  ```
  </Tab>
  <Tab title="JSON">
  ```json title="config.json"
  {
    "models": [
      {
        "title": "DeepSeek R1",
        "provider": "nebius",
        "model": "deepseek-ai/DeepSeek-R1",
        "apiKey": "<YOUR_NEBIUS_API_KEY>"
      }
    ]
  }
  ```
  </Tab>
</Tabs>

## Embeddings model

Available models can be found on the [Nebius AI Studio embeddings page](https://studio.nebius.ai/models/embedding)

<Tabs>
  <Tab title="YAML">
  ```yaml title="config.yaml"
  models:
    - name: BAAI Embedder
      provider: nebius
      model: BAAI/bge-en-icl
      apiKey: <YOUR_NEBIUS_API_KEY>
      roles:
        - embed
  ```
  </Tab>
  <Tab title="JSON">
  ```json title="config.json"
  {
    "embeddingsProvider": {
      "provider": "nebius",
      "model": "BAAI/bge-en-icl",
      "apiKey": "<YOUR_NEBIUS_API_KEY>"
    }
  }
  ```
  </Tab>
</Tabs>
