name: PR checks

# PR Checks run on all PRs to the main branch and must pass before merging.
on:
  pull_request:
    branches:
      - main

  push:
    branches:
      - main

jobs:
  install-root:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        id: root-cache
        with:
          path: node_modules
          key: ${{ runner.os }}-root-node-modules-${{ hashFiles('package-lock.json') }}

      - name: Install root dependencies
        if: steps.root-cache.outputs.cache-hit != 'true'
        run: npm ci

  prettier-check:
    needs: install-root
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-root-node-modules-${{ hashFiles('package-lock.json') }}

      - name: Check code formatting with Prettier
        run: npx prettier --check "**/*.{js,jsx,ts,tsx,json,css,md}" --ignore-path .gitignore --ignore-path .prettierignore

  install-config-yaml:
    needs: install-root
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-root-node-modules-${{ hashFiles('package-lock.json') }}

      - uses: actions/cache@v4
        id: config-yaml-cache
        with:
          path: packages/config-yaml/node_modules
          key: ${{ runner.os }}-config-yaml-node-modules-${{ hashFiles('packages/config-yaml/package-lock.json') }}

      - name: Install config-yaml dependencies
        if: steps.config-yaml-cache.outputs.cache-hit != 'true'
        run: |
          cd packages/config-yaml
          npm ci

  install-openai-adapters:
    needs: install-root
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-root-node-modules-${{ hashFiles('package-lock.json') }}

      - uses: actions/cache@v4
        id: openai-adapters-cache
        with:
          path: packages/openai-adapters/node_modules
          key: ${{ runner.os }}-openai-adapters-node-modules-${{ hashFiles('packages/openai-adapters/package-lock.json') }}

      - name: Install openai-adapters dependencies
        if: steps.openai-adapters-cache.outputs.cache-hit != 'true'
        run: |
          cd packages/openai-adapters
          npm ci

  config-yaml-checks:
    needs: install-config-yaml
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-root-node-modules-${{ hashFiles('package-lock.json') }}

      - uses: actions/cache@v4
        id: config-yaml-cache
        with:
          path: packages/config-yaml/node_modules
          key: ${{ runner.os }}-config-yaml-node-modules-${{ hashFiles('packages/config-yaml/package-lock.json') }}

      - name: Build and check config-yaml
        run: |
          cd packages/config-yaml
          npx tsc --noEmit
          # Tests are currently failing, commenting out for now
          # npm test 
          npm run build

  install-core:
    needs: install-root
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        id: root-cache
        with:
          path: node_modules
          key: ${{ runner.os }}-root-node-modules-${{ hashFiles('package-lock.json') }}

      - uses: actions/cache@v4
        id: core-cache
        with:
          path: core/node_modules
          key: ${{ runner.os }}-core-node-modules-${{ hashFiles('core/package-lock.json') }}

      - name: Install core dependencies
        if: steps.core-cache.outputs.cache-hit != 'true'
        run: |
          cd core
          npm ci

  core-checks:
    needs: [install-core, install-config-yaml, install-openai-adapters]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        id: root-cache
        with:
          path: node_modules
          key: ${{ runner.os }}-root-node-modules-${{ hashFiles('package-lock.json') }}

      - uses: actions/cache@v4
        id: core-cache
        with:
          path: core/node_modules
          key: ${{ runner.os }}-core-node-modules-${{ hashFiles('core/package-lock.json') }}

      - uses: actions/cache@v4
        id: config-yaml-cache
        with:
          path: packages/config-yaml/node_modules
          key: ${{ runner.os }}-config-yaml-node-modules-${{ hashFiles('packages/config-yaml/package-lock.json') }}

      - uses: actions/cache@v4
        id: openai-adapters-cache
        with:
          path: packages/openai-adapters/node_modules
          key: ${{ runner.os }}-openai-adapters-node-modules-${{ hashFiles('packages/openai-adapters/package-lock.json') }}

      - name: Build config-yaml
        run: |
          cd packages/config-yaml
          npm run build

      - name: Build openai-adapters
        run: |
          cd packages/openai-adapters
          npm run build
      - name: Type check and lint
        run: |
          cd core
          npx tsc --noEmit
          npm run lint
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}

  install-gui:
    needs:
      [install-root, install-core, install-config-yaml, install-openai-adapters]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-root-node-modules-${{ hashFiles('package-lock.json') }}

      - uses: actions/cache@v4
        with:
          path: core/node_modules
          key: ${{ runner.os }}-core-node-modules-${{ hashFiles('core/package-lock.json') }}

      - uses: actions/cache@v4
        id: gui-cache
        with:
          path: gui/node_modules
          key: ${{ runner.os }}-gui-node-modules-${{ hashFiles('gui/package-lock.json') }}

      - uses: actions/cache@v4
        id: config-yaml-cache
        with:
          path: packages/config-yaml/node_modules
          key: ${{ runner.os }}-config-yaml-node-modules-${{ hashFiles('packages/config-yaml/package-lock.json') }}

      - uses: actions/cache@v4
        id: openai-adapters-cache
        with:
          path: packages/openai-adapters/node_modules
          key: ${{ runner.os }}-openai-adapters-node-modules-${{ hashFiles('packages/openai-adapters/package-lock.json') }}

      - name: Build config-yaml
        run: |
          cd packages/config-yaml
          npm run build

      - name: Build openai-adapters
        run: |
          cd packages/openai-adapters
          npm run build

      - name: Install gui dependencies
        if: steps.gui-cache.outputs.cache-hit != 'true'
        run: |
          cd gui
          npm ci

  gui-checks:
    needs: install-gui
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-root-node-modules-${{ hashFiles('package-lock.json') }}

      - uses: actions/cache@v4
        with:
          path: core/node_modules
          key: ${{ runner.os }}-core-node-modules-${{ hashFiles('core/package-lock.json') }}

      - uses: actions/cache@v4
        id: gui-cache
        with:
          path: gui/node_modules
          key: ${{ runner.os }}-gui-node-modules-${{ hashFiles('gui/package-lock.json') }}

      - uses: actions/cache@v4
        id: openai-adapters-cache
        with:
          path: packages/openai-adapters/node_modules
          key: ${{ runner.os }}-openai-adapters-node-modules-${{ hashFiles('packages/openai-adapters/package-lock.json') }}

      - uses: actions/cache@v4
        id: config-yaml-cache
        with:
          path: packages/config-yaml/node_modules
          key: ${{ runner.os }}-config-yaml-node-modules-${{ hashFiles('packages/config-yaml/package-lock.json') }}

      - name: Build config-yaml
        run: |
          cd packages/config-yaml
          npm run build

      - name: Build openai-adapters
        run: |
          cd packages/openai-adapters
          npm run build

      - name: Type check and lint
        run: |
          cd gui
          npx tsc --noEmit
          npm run lint

  binary-checks:
    needs:
      [install-root, install-core, install-config-yaml, install-openai-adapters]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-root-node-modules-${{ hashFiles('package-lock.json') }}

      - uses: actions/cache@v4
        with:
          path: core/node_modules
          key: ${{ runner.os }}-core-node-modules-${{ hashFiles('core/package-lock.json') }}

      - uses: actions/cache@v4
        id: binary-cache
        with:
          path: binary/node_modules
          key: ${{ runner.os }}-binary-node-modules-${{ hashFiles('binary/package-lock.json') }}

      - uses: actions/cache@v4
        id: config-yaml-cache
        with:
          path: packages/config-yaml/node_modules
          key: ${{ runner.os }}-config-yaml-node-modules-${{ hashFiles('packages/config-yaml/package-lock.json') }}

      - uses: actions/cache@v4
        id: openai-adapters-cache
        with:
          path: packages/openai-adapters/node_modules
          key: ${{ runner.os }}-openai-adapters-node-modules-${{ hashFiles('packages/openai-adapters/package-lock.json') }}

      - name: Build config-yaml
        run: |
          cd packages/config-yaml
          npm run build

      - name: Build openai-adapters
        run: |
          cd packages/openai-adapters
          npm run build

      - name: Install binary dependencies
        if: steps.binary-cache.outputs.cache-hit != 'true'
        run: |
          cd binary
          npm ci

      - name: Type check
        run: |
          cd binary
          npx tsc --noEmit

  install-vscode:
    needs:
      [install-root, install-core, install-config-yaml, install-openai-adapters]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-root-node-modules-${{ hashFiles('package-lock.json') }}

      - uses: actions/cache@v4
        with:
          path: core/node_modules
          key: ${{ runner.os }}-core-node-modules-${{ hashFiles('core/package-lock.json') }}

      - uses: actions/cache@v4
        id: vscode-cache
        with:
          path: extensions/vscode/node_modules
          key: ${{ runner.os }}-vscode-node-modules-${{ hashFiles('extensions/vscode/package-lock.json') }}

      - uses: actions/cache@v4
        id: config-yaml-cache
        with:
          path: packages/config-yaml/node_modules
          key: ${{ runner.os }}-config-yaml-node-modules-${{ hashFiles('packages/config-yaml/package-lock.json') }}

      - uses: actions/cache@v4
        id: openai-adapters-cache
        with:
          path: packages/openai-adapters/node_modules
          key: ${{ runner.os }}-openai-adapters-node-modules-${{ hashFiles('packages/openai-adapters/package-lock.json') }}

      - name: Build config-yaml
        run: |
          cd packages/config-yaml
          npm run build

      - name: Build openai-adapters
        run: |
          cd packages/openai-adapters
          npm run build

      - name: Install vscode dependencies
        if: steps.vscode-cache.outputs.cache-hit != 'true'
        run: |
          cd extensions/vscode
          npm ci
        env:
          GITHUB_TOKEN: ${{ secrets.CI_GITHUB_TOKEN }}

  vscode-checks:
    needs: install-vscode
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-root-node-modules-${{ hashFiles('package-lock.json') }}

      - uses: actions/cache@v4
        with:
          path: core/node_modules
          key: ${{ runner.os }}-core-node-modules-${{ hashFiles('core/package-lock.json') }}

      - uses: actions/cache@v4
        id: vscode-cache
        with:
          path: extensions/vscode/node_modules
          key: ${{ runner.os }}-vscode-node-modules-${{ hashFiles('extensions/vscode/package-lock.json') }}

      - uses: actions/cache@v4
        id: config-yaml-cache
        with:
          path: packages/config-yaml/node_modules
          key: ${{ runner.os }}-config-yaml-node-modules-${{ hashFiles('packages/config-yaml/package-lock.json') }}

      - uses: actions/cache@v4
        id: openai-adapters-cache
        with:
          path: packages/openai-adapters/node_modules
          key: ${{ runner.os }}-openai-adapters-node-modules-${{ hashFiles('packages/openai-adapters/package-lock.json') }}

      - name: Build config-yaml
        run: |
          cd packages/config-yaml
          npm run build

      - name: Build openai-adapters
        run: |
          cd packages/openai-adapters
          npm run build

      - name: Type check and lint
        run: |
          cd extensions/vscode
          npm run write-build-timestamp
          npx tsc --noEmit
          npm run lint

      - name: Run vitest tests
        run: |
          cd extensions/vscode
          npm run vitest

  core-tests:
    needs: [install-core, install-config-yaml, install-openai-adapters]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        with:
          path: core/node_modules
          key: ${{ runner.os }}-core-node-modules-${{ hashFiles('core/package-lock.json') }}

      - uses: actions/cache@v4
        id: config-yaml-cache
        with:
          path: packages/config-yaml/node_modules
          key: ${{ runner.os }}-config-yaml-node-modules-${{ hashFiles('packages/config-yaml/package-lock.json') }}

      - uses: actions/cache@v4
        id: openai-adapters-cache
        with:
          path: packages/openai-adapters/node_modules
          key: ${{ runner.os }}-openai-adapters-node-modules-${{ hashFiles('packages/openai-adapters/package-lock.json') }}

      - name: Build config-yaml
        run: |
          cd packages/config-yaml
          npm run build

      - name: Build openai-adapters
        run: |
          cd packages/openai-adapters
          npm run build

      - name: Run core tests
        run: |
          cd core
          npm test
          npm run vitest
        env:
          IGNORE_API_KEY_TESTS: ${{ github.event.pull_request.head.repo.fork == true || github.actor == 'dependabot[bot]' }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
          GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
          MISTRAL_API_KEY: ${{ secrets.MISTRAL_API_KEY }}
          AZURE_OPENAI_API_KEY: ${{ secrets.AZURE_OPENAI_API_KEY }}
          AZURE_FOUNDRY_CODESTRAL_API_KEY: ${{ secrets.AZURE_FOUNDRY_CODESTRAL_API_KEY }}
          AZURE_FOUNDRY_MISTRAL_SMALL_API_KEY: ${{ secrets.AZURE_FOUNDRY_MISTRAL_SMALL_API_KEY }}
          AZURE_OPENAI_GPT41_API_KEY: ${{ secrets.AZURE_OPENAI_GPT41_API_KEY }}

  package-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        with:
          path: core/node_modules
          key: ${{ runner.os }}-config-yaml-node-modules-${{ hashFiles('packages/config-yaml/package-lock.json') }}

      - name: Install dependencies
        run: |
          cd packages/config-yaml
          npm ci

      - name: Run config-yaml tests
        run: |
          cd packages/config-yaml
          npm test

  vscode-get-test-file-matrix:
    runs-on: ubuntu-latest
    needs: [install-root, install-vscode, install-config-yaml]
    outputs:
      test_file_matrix: ${{ steps.vscode-get-test-file-matrix.outputs.test_file_matrix }}
    steps:
      - uses: actions/checkout@v4

      - name: Cache node modules
        uses: actions/cache@v4
        with:
          path: extensions/vscode/node_modules
          key: ${{ runner.os }}-vscode-node-modules-${{ hashFiles('extensions/vscode/package-lock.json') }}

      - uses: actions/cache@v4
        id: config-yaml-cache
        with:
          path: packages/config-yaml/node_modules
          key: ${{ runner.os }}-config-yaml-node-modules-${{ hashFiles('packages/config-yaml/package-lock.json') }}

      - name: Build config-yaml
        run: |
          cd packages/config-yaml
          npm run build

      - name: Get test files
        id: vscode-get-test-file-matrix
        run: |
          cd extensions/vscode
          npm ci
          npm run e2e:compile
          if [[ "${{ github.event.pull_request.head.repo.fork }}" == "true" || "${{ github.actor }}" == "dependabot[bot]" ]]; then
            # Exclude SSH tests for forks
            FILES=$(ls -1 e2e/_output/tests/*.test.js | grep -v "SSH" | jq -R . | jq -s .)
          else
            # Include all tests for non-forks
            FILES=$(ls -1 e2e/_output/tests/*.test.js | jq -R . | jq -s .)
          fi
          echo "test_file_matrix<<EOF" >> $GITHUB_OUTPUT
          echo "$FILES" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Debug Outputs
        run: |
          echo "Test files: ${{ steps.vscode-get-test-file-matrix.outputs.test_file_matrix }}"

  vscode-package-extension:
    runs-on: ubuntu-latest
    needs:
      [
        install-vscode,
        install-core,
        install-config-yaml,
        install-openai-adapters,
      ]
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        id: vscode-node-modules-cache
        with:
          path: extensions/vscode/node_modules
          key: ${{ runner.os }}-vscode-node-modules-${{ hashFiles('extensions/vscode/package-lock.json') }}

      - uses: actions/cache@v4
        id: core-node-modules-cache
        with:
          path: core/node_modules
          key: ${{ runner.os }}-core-node-modules-${{ hashFiles('core/package-lock.json') }}

      - uses: actions/cache@v4
        id: config-yaml-cache
        with:
          path: packages/config-yaml/node_modules
          key: ${{ runner.os }}-config-yaml-node-modules-${{ hashFiles('packages/config-yaml/package-lock.json') }}

      - uses: actions/cache@v4
        id: openai-adapters-cache
        with:
          path: packages/openai-adapters/node_modules
          key: ${{ runner.os }}-openai-adapters-node-modules-${{ hashFiles('packages/openai-adapters/package-lock.json') }}

      - name: Build config-yaml
        run: |
          cd packages/config-yaml
          npm run build

      - name: Build openai-adapters
        run: |
          cd packages/openai-adapters
          npm run build

      - name: Package extension
        run: |
          cd extensions/vscode
          npm run package

      - name: Upload build artifact
        uses: actions/upload-artifact@v4
        with:
          name: vscode-extension-build
          path: extensions/vscode/build

  vscode-download-e2e-dependencies:
    runs-on: ubuntu-latest
    needs:
      [
        install-vscode,
        install-core,
        install-config-yaml,
        install-openai-adapters,
      ]
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        id: vscode-node-modules-cache
        with:
          path: extensions/vscode/node_modules
          key: ${{ runner.os }}-vscode-node-modules-${{ hashFiles('extensions/vscode/package-lock.json') }}

      - uses: actions/cache@v4
        id: storage-cache
        with:
          path: extensions/vscode/e2e/storage
          key: ${{ runner.os }}-vscode-storage-${{ hashFiles('extensions/vscode/package-lock.json') }}

      - uses: actions/cache@v4
        id: config-yaml-cache
        with:
          path: packages/config-yaml/node_modules
          key: ${{ runner.os }}-config-yaml-node-modules-${{ hashFiles('packages/config-yaml/package-lock.json') }}

      - name: Build config-yaml
        run: |
          cd packages/config-yaml
          npm run build

      - name: Download Dependencies
        if: steps.storage-cache.outputs.cache-hit != 'true'
        run: |
          cd extensions/vscode
          npm run e2e:ci:download

      - name: Upload e2e dependencies
        uses: actions/upload-artifact@v4
        with:
          name: vscode-e2e-dependencies
          path: extensions/vscode/e2e/storage

  vscode-e2e-tests:
    name: ${{ matrix.test_file || 'unknown' }} (${{ matrix.command }})
    needs:
      [
        vscode-download-e2e-dependencies,
        vscode-get-test-file-matrix,
        vscode-package-extension,
        install-vscode,
        install-core,
        install-config-yaml,
        install-openai-adapters,
      ]
    runs-on: ubuntu-latest
    # Tests requiring secrets need approval from maintainers
    strategy:
      fail-fast: false
      matrix:
        test_file: ${{ fromJson(needs.vscode-get-test-file-matrix.outputs.test_file_matrix) }}
        command: ["e2e:ci:run", "e2e:ci:run-yaml"]
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        id: vscode-node-modules-cache
        with:
          path: extensions/vscode/node_modules
          key: ${{ runner.os }}-vscode-node-modules-${{ hashFiles('extensions/vscode/package-lock.json') }}

      # We don't want to cache the Continue extension, so it is deleted at the end of the job
      - uses: actions/cache@v4
        id: test-extensions-cache
        with:
          path: extensions/vscode/e2e/.test-extensions
          key: CONSTANT

      - name: Download build artifact
        uses: actions/download-artifact@v4
        with:
          name: vscode-extension-build
          path: extensions/vscode/build

      - name: Download e2e dependencies
        uses: actions/download-artifact@v4
        with:
          name: vscode-e2e-dependencies
          path: extensions/vscode/e2e/storage

      - uses: actions/cache@v4
        id: config-yaml-cache
        with:
          path: packages/config-yaml/node_modules
          key: ${{ runner.os }}-config-yaml-node-modules-${{ hashFiles('packages/config-yaml/package-lock.json') }}

      - uses: actions/cache@v4
        id: openai-adapters-cache
        with:
          path: packages/openai-adapters/node_modules
          key: ${{ runner.os }}-openai-adapters-node-modules-${{ hashFiles('packages/openai-adapters/package-lock.json') }}

      - name: Build config-yaml
        run: |
          cd packages/config-yaml
          npm run build

      - name: Build openai-adapters
        run: |
          cd packages/openai-adapters
          npm run build

      - name: Fix VSCode binary permissions
        run: |
          chmod +x extensions/vscode/e2e/storage/VSCode-linux-x64/code
          chmod +x extensions/vscode/e2e/storage/chromedriver-linux64/chromedriver

      - name: Set up SSH
        env:
          SSH_KEY: ${{ secrets.GH_ACTIONS_SSH_TEST_KEY_PEM }}
          SSH_HOST: ${{ secrets.GH_ACTIONS_SSH_TEST_DNS_NAME }}
        run: |
          mkdir -p ~/.ssh
          echo "$SSH_KEY" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H "$SSH_HOST" >> ~/.ssh/known_hosts
          echo -e "Host ssh-test-container\n\tHostName $SSH_HOST\n\tUser ec2-user\n\tIdentityFile ~/.ssh/id_rsa" >> ~/.ssh/config
        if: ${{ github.event.pull_request.head.repo.fork == false && github.actor != 'dependabot[bot]' }}

      - name: Set up Xvfb
        run: |
          Xvfb :99 &
          export DISPLAY=:99

      - name: Run e2e tests
        run: |
          cd extensions/vscode
          IGNORE_SSH_TESTS="${{ github.event.pull_request.head.repo.fork == true || github.actor == 'dependabot[bot]' }}" TEST_FILE="${{ matrix.test_file }}" npm run ${{ matrix.command }}
        env:
          DISPLAY: :99

      - name: Delete continue from test extensions
        run: |
          cd extensions/vscode
          rm -rf e2e/.test-extensions/continue*

      - name: Sanitize test file name
        id: sanitize_filename
        if: always()
        run: |
          FILENAME="${{ matrix.test_file }}"
          SANITIZED_FILENAME="${FILENAME//\//-}" # Replace / with - using bash parameter expansion
          echo "sanitized_test_file=${SANITIZED_FILENAME}" >> $GITHUB_OUTPUT

      - name: Upload e2e test screenshots
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: e2e-failure-screenshots-${{ steps.sanitize_filename.outputs.sanitized_test_file || 'unknown' }}-${{ matrix.command == 'e2e:ci:run-yaml' && 'yaml' || 'json' }}
          path: extensions/vscode/e2e/storage/screenshots

      - name: Find e2e log file
        if: always()
        run: |
          echo "Searching for e2e.log file..."
          find $GITHUB_WORKSPACE -name "e2e.log" -type f

      - name: Upload e2e logs
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: e2e-logs-${{ steps.sanitize_filename.outputs.sanitized_test_file || 'unknown' }}-${{ matrix.command == 'e2e:ci:run-yaml' && 'yaml' || 'json' }}
          path: extensions/vscode/e2e.log

  gui-tests:
    needs:
      [install-gui, install-core, install-config-yaml, install-openai-adapters]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        id: gui-cache
        with:
          path: gui/node_modules
          key: ${{ runner.os }}-gui-node-modules-${{ hashFiles('gui/package-lock.json') }}

      - uses: actions/cache@v4
        with:
          path: core/node_modules
          key: ${{ runner.os }}-core-node-modules-${{ hashFiles('core/package-lock.json') }}

      - uses: actions/cache@v4
        id: config-yaml-cache
        with:
          path: packages/config-yaml/node_modules
          key: ${{ runner.os }}-config-yaml-node-modules-${{ hashFiles('packages/config-yaml/package-lock.json') }}

      - uses: actions/cache@v4
        id: openai-adapters-cache
        with:
          path: packages/openai-adapters/node_modules
          key: ${{ runner.os }}-openai-adapters-node-modules-${{ hashFiles('packages/openai-adapters/package-lock.json') }}

      - name: Build config-yaml
        run: |
          cd packages/config-yaml
          npm run build

      - name: Build openai-adapters
        run: |
          cd packages/openai-adapters
          npm run build

      - name: Install GUI dependencies
        if: steps.gui-cache.outputs.cache-hit != 'true'
        run: cd gui && npm ci
        env:
          GITHUB_TOKEN: ${{ secrets.CI_GITHUB_TOKEN }}

      - name: Run gui tests
        run: |
          cd gui
          npm test

  jetbrains-tests:
    needs:
      [install-root, core-checks, install-config-yaml, install-openai-adapters]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/cache@v4
        with:
          path: core/node_modules
          key: ${{ runner.os }}-core-node-modules-${{ hashFiles('core/package-lock.json') }}

      - uses: actions/cache@v4
        id: config-yaml-cache
        with:
          path: packages/config-yaml/node_modules
          key: ${{ runner.os }}-config-yaml-node-modules-${{ hashFiles('packages/config-yaml/package-lock.json') }}

      - uses: actions/cache@v4
        id: openai-adapters-cache
        with:
          path: packages/openai-adapters/node_modules
          key: ${{ runner.os }}-openai-adapters-node-modules-${{ hashFiles('packages/openai-adapters/package-lock.json') }}

      - name: Build config-yaml
        run: |
          cd packages/config-yaml
          npm run build

      - name: Build openai-adapters
        run: |
          cd packages/openai-adapters
          npm run build

      - name: Setup Java
        uses: actions/setup-java@v4.5.0
        with:
          distribution: zulu
          java-version: 17

      - name: Setup FFmpeg
        uses: AnimMouse/setup-ffmpeg@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v3

      - name: Use Node.js from .nvmrc
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        id: gui-cache
        with:
          path: gui/node_modules
          key: ${{ runner.os }}-gui-node-modules-${{ hashFiles('gui/package-lock.json') }}

      # We can shave off another minute off our CI script by finding a way to share this with vscode-tests
      - name: Run prepackage script
        run: |
          cd extensions/vscode
          npm ci
          npm run prepackage
        env:
          # https://github.com/microsoft/vscode-ripgrep/issues/9#issuecomment-643965333
          GITHUB_TOKEN: ${{ secrets.CI_GITHUB_TOKEN }}

      - uses: actions/cache@v4
        id: binary-cache
        with:
          path: binary/node_modules
          key: ${{ runner.os }}-binary-node-modules-${{ hashFiles('binary/package-lock.json') }}

      - name: Build the binaries
        run: |
          cd binary
          npm run build

      - name: Start test IDE
        run: |
          cd extensions/intellij
          export DISPLAY=:99.0
          Xvfb -ac :99 -screen 0 1920x1080x24 &
          sleep 10
          mkdir -p build/reports
          ./gradlew runIdeForUiTests &

      - name: Wait for JB connection
        uses: jtalk/url-health-check-action@v3
        with:
          url: http://127.0.0.1:8082
          max-attempts: 15
          retry-delay: 30s

      - name: Run tests
        run: |
          cd extensions/intellij
          export DISPLAY=:99.0
          ./gradlew test

      - name: Move video
        if: ${{ failure() }}
        run: |
          cd extensions/intellij
          mv video build/reports

      - name: Copy logs
        if: ${{ failure() }}
        run: |
          cd extensions/intellij
          mv build/idea-sandbox/system/log/ build/reports

      - name: Save fails report
        if: ${{ failure() }}
        uses: actions/upload-artifact@v4
        with:
          name: jb-failure-report
          path: |
            ${{ github.workspace }}/extensions/intellij/build/reports

  # GitHub does not have a way of requiring that all checks pass (you must manually select each job)
  # This action at least lets us manage the list of required tests via source control
  # so that creators of new jobs can add them to this list
  require-all-checks-to-pass:
    if: always()
    runs-on: ubuntu-latest
    needs:
      - install-root
      - prettier-check
      - install-core
      - core-checks
      - install-gui
      - gui-checks
      - binary-checks
      - install-vscode
      - vscode-checks
      - core-tests
      - package-tests
      - vscode-get-test-file-matrix
      - vscode-package-extension
      - vscode-download-e2e-dependencies
      - vscode-e2e-tests
      - gui-tests
      - jetbrains-tests
      - config-yaml-checks
      - install-config-yaml
      - install-openai-adapters

    steps:
      - name: Decide whether the needed jobs succeeded or failed
        uses: re-actors/alls-green@release/v1
        with:
          jobs: ${{ toJSON(needs) }}
