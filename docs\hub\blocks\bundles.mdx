---
title: "Bundles"
description: "Bundles are collections of blocks that are commonly used together. You can use them to add multiple building blocks to your custom AI code assistants at once. They are only a concept on , so they are not represented in ."
---

## Use a bundle

Once you know which bundle you want to use, you’ll need to

1. Make sure the correct assistant is in the sidebar
2. Click “Add all blocks". This adds the individual blocks to your assistant.
3. Add any required inputs (e.g. secrets) for each block.
4. Select “Save changes” in assistant sidebar on the right hand side

After this, you can then go to your IDE extension using the "Open VS Code" or "Open Jetbrains" buttons and begin using the new blocks.

## Create a bundle

To create a bundle, click “New bundle” in the header.

![New bundle button](/images/hub/blocks/images/bundle-new-button-4e6ae842500100fffeff8b39f74728f0.png)

Choose a name, slug, description, and visibility for your bundle.

Then, search blocks using the "Search blocks" input and add them to your bundle.

![Create bundle page](/images/hub/blocks/images/bundle-create-page-51dc9d72652c6675edb2e2a085db16a1.png)

Once you have added all the blocks you want in your bundle, click "Create Bundle" to save it and make it available for use.

## Remix a bundle

It is not currently possible to remix a bundle.
