## Add Rules Blocks

Adding Rules can be done in your assistant locally or in the Hub. You can explore Rules on the Continue Hub and refer to the [Rules deep dive](/customize/deep-dives/rules) for more details.

## Add MCP Tools

You can add MCP servers to your assistant to give Agent access to more tools. Explore [MCP Servers on the Hub](https://hub.continue.dev) and consult the [MCP guide](/customize/deep-dives/mcp) for more details.

## Tool Policies

You can adjust the Agent's tool usage behavior to three options:

- **Ask First (default)**: Request user permission with "Cancel" and "Continue" buttons
- **Automatic**: Automatically call the tool without requesting permission
- **Excluded**: Do not send the tool to the model

:::warning
Be careful setting tools to "automatic" if their behavior is not read-only.
:::

To manage tool policies:

1. Click the tools icon in the input toolbar
2. View and change policies by clicking on the policy text
3. You can also toggle groups of tools on/off

Tool policies are stored locally per user.
